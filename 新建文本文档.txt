Windows PowerShell
版权所有 (C) Microsoft Corporation。保留所有权利。

尝试新的跨平台 PowerShell https://aka.ms/pscore6

PS C:\Windows\system32> Set-ExecutionPolicy Bypass -Scope Process -Force
PS C:\Windows\system32> [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
PS C:\Windows\system32> iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
警告: 'choco' was found at 'C:\ProgramData\chocolatey\bin\choco.exe'.
警告: An existing Chocolatey installation was detected. Installation will not continue. This script will not overwrite
existing installations.
If there is no Chocolatey installation at 'C:\ProgramData\chocolatey', delete the folder and attempt the installation
again.

Please use choco upgrade chocolatey to handle upgrades of Chocolatey itself.
If the existing installation is not functional or a prior installation did not complete, follow these steps:
 - Backup the files at the path listed above so you can restore your previous installation if needed.
 - Remove the existing installation manually.
 - Rerun this installation script.
 - Reinstall any packages previously installed, if needed (refer to the lib folder in the backup).

Once installation is completed, the backup folder is no longer needed and can be deleted.
PS C:\Windows\system32> choco install openjdk17 -y
Chocolatey v2.4.1
Installing the following packages:
openjdk17
By installing, you accept licenses for the packages.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading openjdk17 17.0.2.20220913... 100%

openjdk17 v17.0.2.20220913 [Approved]
openjdk17 package files install completed. Performing other installation steps.
 The install of openjdk17 was successful.
  Deployed to 'C:\ProgramData\chocolatey\lib\openjdk17'

Chocolatey installed 1/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).
PS C:\Windows\system32> choco install mysql -y
Chocolatey v2.4.1
Installing the following packages:
mysql
By installing, you accept licenses for the packages.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading chocolatey-windowsupdate.extension 1.0.5... 100%

chocolatey-windowsupdate.extension v1.0.5 [Approved]
chocolatey-windowsupdate.extension package files install completed. Performing other installation steps.
 Installed/updated chocolatey-windowsupdate extensions.
 The install of chocolatey-windowsupdate.extension was successful.
  Deployed to 'C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate'
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading ********* 1.0.20160915... 100%

********* v1.0.20160915 [Approved]
********* package files install completed. Performing other installation steps.
Skipping installation because this hotfix only applies to Windows 8.1 and Windows Server 2012 R2.
 The install of ********* was successful.
  Software install location not explicitly set, it could be in package or
  default install location of installer.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading ********* 1.0.20160915... 100%

********* v1.0.20160915 [Approved]
********* package files install completed. Performing other installation steps.
Skipping installation because this hotfix only applies to Windows 8.1 and Windows Server 2012 R2.
 The install of ********* was successful.
  Software install location not explicitly set, it could be in package or
  default install location of installer.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading ********* 1.0.20181019... 100%

********* v1.0.20181019 [Approved]
********* package files install completed. Performing other installation steps.
Skipping installation because update ********* does not apply to this operating system (Microsoft Windows 10 专业版).
 The install of ********* was successful.
  Software install location not explicitly set, it could be in package or
  default install location of installer.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading ********* 1.0.3... 100%

********* v1.0.3 [Approved]
********* package files install completed. Performing other installation steps.
Skipping installation because update ********* does not apply to this operating system (Microsoft Windows 10 专业版).
 The install of ********* was successful.
  Software install location not explicitly set, it could be in package or
  default install location of installer.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading ********* 1.0.5... 100%

********* v1.0.5 [Approved]
********* package files install completed. Performing other installation steps.
Skipping installation because update ********* does not apply to this operating system (Microsoft Windows 10 专业版).
 The install of ********* was successful.
  Software install location not explicitly set, it could be in package or
  default install location of installer.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading vcredist140 14.44.35208... 100%

vcredist140 v14.44.35208 [Approved]
vcredist140 package files install completed. Performing other installation steps.
Using system proxy server '127.0.0.1:21882'.
Downloading vcredist140-x86
  from 'https://download.visualstudio.microsoft.com/download/pr/40b59c73-1480-4caf-ab5b-4886f176bf71/435A0DE411B991E2BFC7FD1D5439639E7B32206960D3099370E36172018F52FE/VC_redist.x86.exe'
Using system proxy server '127.0.0.1:21882'.
Progress: 100% - Completed download of D:\Users\jsxzxhx\AppData\Local\Temp\chocolatey\vcredist140\14.44.35208\VC_redist.x86.exe (13.31 MB).
Download of VC_redist.x86.exe (13.31 MB) completed.
Hashes match.
Installing vcredist140-x86...
vcredist140-x86 has been installed.
Using system proxy server '127.0.0.1:21882'.
Downloading vcredist140-x64 64 bit
  from 'https://download.visualstudio.microsoft.com/download/pr/40b59c73-1480-4caf-ab5b-4886f176bf71/D62841375B90782B1829483AC75695CCEF680A8F13E7DE569B992EF33C6CD14A/VC_redist.x64.exe'
Using system proxy server '127.0.0.1:21882'.
Progress: 100% - Completed download of D:\Users\jsxzxhx\AppData\Local\Temp\chocolatey\vcredist140\14.44.35208\VC_redist.x64.exe (24.45 MB).
Download of VC_redist.x64.exe (24.45 MB) completed.
Hashes match.
Installing vcredist140-x64...
vcredist140-x64 has been installed.
  vcredist140 may be able to be automatically uninstalled.
 The install of vcredist140 was successful.
  Software installed as 'exe', install location is likely default.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading vcredist2015 14.0.24215.20170201... 100%

vcredist2015 v14.0.24215.20170201 [Approved]
vcredist2015 package files install completed. Performing other installation steps.
 The install of vcredist2015 was successful.
  Deployed to 'C:\ProgramData\chocolatey\lib\vcredist2015'
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading mysql 9.2.0... 100%

mysql v9.2.0 [Approved] - Likely broken for FOSS users (due to download location changes)
mysql package files install completed. Performing other installation steps.
Adding 'C:\tools\mysql\current\bin' to the path and the current shell path
PATH environment variable does not have C:\tools\mysql\current\bin in it. Adding...
Using system proxy server '127.0.0.1:21882'.
Downloading mysql 64 bit
  from 'https://cdn.mysql.com/Downloads/MySQL-9.2/mysql-9.2.0-winx64.zip'
Using system proxy server '127.0.0.1:21882'.
Progress: 100% - Completed download of D:\Users\jsxzxhx\AppData\Local\Temp\mysql\9.2.0\mysql-9.2.0-winx64.zip (290.11 MB).
Download of mysql-9.2.0-winx64.zip (290.11 MB) completed.
Hashes match.
Extracting D:\Users\jsxzxhx\AppData\Local\Temp\mysql\9.2.0\mysql-9.2.0-winx64.zip to C:\tools\mysql...
C:\tools\mysql
Shutting down MySQL if it is running
#< CLIXML
<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块 。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><S S="debug">Host version is 5.1.19041.1237, PowerShell Version is '5.1.19041.1237' and CLR Version is '4.0.30319.42000'.</S><S S="verbose"> 正在从路径“C:\ProgramData\chocolatey\helpers\Chocolatey.PowerShell.dll”加载模块。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导 入 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose"> 正在导入 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导入 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Update-SessionEnvironment”。</S><S S="debug">Cmdlets exported from Chocolatey.PowerShell.dll</S><S S="debug">Get-EnvironmentVariable</S><S S="debug">Get-EnvironmentVariableNames</S><S S="debug">Install-ChocolateyPath</S><S S="debug">Set-EnvironmentVariable</S><S S="debug">Test-ProcessAdminRights</S><S S="debug">Uninstall-ChocolateyPath</S><S S="debug">Update-SessionEnvironment</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在 导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数 “Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正 在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S><S S="debug">Loading community extensions</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1”加载模块。</S><S S="debug">Function 'Get-PackageParameters' exists, ignoring export.</S><S S="debug">Function 'Get-UninstallRegistryKey' exists, ignoring export.</S><S S="debug">Exporting function 'Install-ChocolateyDesktopLink' for backwards compatibility</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="debug">Exporting function 'Write-ChocolateyFailure' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="debug">Exporting function 'Write-ChocolateySuccess' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="debug">Exporting function 'Write-FileUpdateLog' for backwards compatibility</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导入函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导入函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导入函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导入函数“Write-FileUpdateLog”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1”加载模块。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函 数“Remove-Process”。</S><S S="verbose">正在导入函数“Get-AppInstallLocation”。</S><S S="verbose">正在导入函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导入函数“Get-EffectiveProxy”。</S><S S="verbose">正在导入函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导入函数服务名无效。
请键入 NET HELPMSG 2185 以获得更多的帮助。
Microsoft.PowerShell.Commands.WriteErrorException
Microsoft.PowerShell.Commands.WriteErrorException
“Get-WebContent”。</S><S S="verbose">正在导入函数“Register-Application”。</S><S S="verbose">正在导入函数“Remove-Process”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1”加载模块。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导入函数“Install-WindowsUpdate”。</S><S S="verbose">正在导入函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在 导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose"> 正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数“Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导 出函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函 数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函数“Remove-Process”。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable ”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S></Objs>
0
#< CLIXML
<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块 。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><S S="debug">Host version is 5.1.19041.1237, PowerShell Version is '5.1.19041.1237' and CLR Version is '4.0.30319.42000'.</S><S S="verbose"> 正在从路径“C:\ProgramData\chocolatey\helpers\Chocolatey.PowerShell.dll”加载模块。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导 入 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose"> 正在导入 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导入 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Update-SessionEnvironment”。</S><S S="debug">Cmdlets exported from Chocolatey.PowerShell.dll</S><S S="debug">Get-EnvironmentVariable</S><S S="debug">Get-EnvironmentVariableNames</S><S S="debug">Install-ChocolateyPath</S><S S="debug">Set-EnvironmentVariable</S><S S="debug">Test-ProcessAdminRights</S><S S="debug">Uninstall-ChocolateyPath</S><S S="debug">Update-SessionEnvironment</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在 导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数 “Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正 在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S><S S="debug">Loading community extensions</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1”加载模块。</S><S S="debug">Function 'Get-PackageParameters' exists, ignoring export.</S><S S="debug">Function 'Get-UninstallRegistryKey' exists, ignoring export.</S><S S="debug">Exporting function 'Install-ChocolateyDesktopLink' for backwards compatibility</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="debug">Exporting function 'Write-ChocolateyFailure' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="debug">Exporting function 'Write-ChocolateySuccess' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="debug">Exporting function 'Write-FileUpdateLog' for backwards compatibility</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导入函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导入函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导入函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导入函数“Write-FileUpdateLog”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1”加载模块。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函 数“Remove-Process”。</S><S S="verbose">正在导入函数“Get-AppInstallLocation”。</S><S S="verbose">正在导入函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导入函数“Get-EffectiveProxy”。</S><S S="verbose">正在导入函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导入函数“Get-WebContent”。</S><S S="verbose">正在导入函数“Register-Application”。</S><S S="verbose">正在导入函数“Remove-Process”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1”加载模块。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导入函数“Install-WindowsUpdate”。</S><S S="verbose">正在导入函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在 导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数“Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函数“Remove-Process”。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet “Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正 在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正 在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名 “Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S></Objs>
0
Copying contents of 'C:\tools\mysql\mysql-9.2.0-winx64' to 'C:\tools\mysql\current'.
No existing my.ini. Creating default 'C:\tools\mysql\current\my.ini' with default locations for datadir.
Initializing MySQL if it hasn't already been initialized.
#< CLIXML
<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块 。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><S S="debug">Host version is 5.1.19041.1237, PowerShell Version is '5.1.19041.1237' and CLR Version is '4.0.30319.42000'.</S><S S="verbose"> 正在从路径“C:\ProgramData\chocolatey\helpers\Chocolatey.PowerShell.dll”加载模块。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导 入 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose"> 正在导入 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导入 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Update-SessionEnvironment”。</S><S S="debug">Cmdlets exported from Chocolatey.PowerShell.dll</S><S S="debug">Get-EnvironmentVariable</S><S S="debug">Get-EnvironmentVariableNames</S><S S="debug">Install-ChocolateyPath</S><S S="debug">Set-EnvironmentVariable</S><S S="debug">Test-ProcessAdminRights</S><S S="debug">Uninstall-ChocolateyPath</S><S S="debug">Update-SessionEnvironment</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在 导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数 “Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正 在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S><S S="debug">Loading community extensions</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1”加载模块。</S><S S="debug">Function 'Get-PackageParameters' exists, ignoring export.</S><S S="debug">Function 'Get-UninstallRegistryKey' exists, ignoring export.</S><S S="debug">Exporting function 'Install-ChocolateyDesktopLink' for backwards compatibility</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="debug">Exporting function 'Write-ChocolateyFailure' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="debug">Exporting function 'Write-ChocolateySuccess' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="debug">Exporting function 'Write-FileUpdateLog' for backwards compatibility</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导入函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导入函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导入函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导入函数“Write-FileUpdateLog”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1”加载模块。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函 数“Remove-Process”。</S><S S="verbose">正在导入函数“Get-AppInstallLocation”。</S><S S="verbose">正在导入函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导入函数“Get-EffectiveProxy”。</S><S S="verbose">正在导入函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导入函数“Get-WebContent”。</S><S S="verbose">正在导入函数“Register-Application”。</S><S S="verbose">正在导入函数“Remove-Process”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1”加载模块。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导入函数“Install-WindowsUpdate”。</S><S S="verbose">正在导入函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在 导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数“Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函数“Remove-Process”。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet “Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正 在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正 在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名 “Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S></Objs>
0
Installing the mysql service
#< CLIXML
<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块 。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><S S="debug">Host version is 5.1.19041.1237, PowerShell Version is '5.1.19041.1237' and CLR Version is '4.0.30319.42000'.</S><S S="verbose"> 正在从路径“C:\ProgramData\chocolatey\helpers\Chocolatey.PowerShell.dll”加载模块。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导 入 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose"> 正在导入 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导入 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Update-SessionEnvironment”。</S><S S="debug">Cmdlets exported from Chocolatey.PowerShell.dll</S><S S="debug">Get-EnvironmentVariable</S><S S="debug">Get-EnvironmentVariableNames</S><S S="debug">Install-ChocolateyPath</S><S S="debug">Set-EnvironmentVariable</S><S S="debug">Test-ProcessAdminRights</S><S S="debug">Uninstall-ChocolateyPath</S><S S="debug">Update-SessionEnvironment</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在 导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数 “Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正 在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S><S S="debug">Loading community extensions</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1”加载模块。</S><S S="debug">Function 'Get-PackageParameters' exists, ignoring export.</S><S S="debug">Function 'Get-UninstallRegistryKey' exists, ignoring export.</S><S S="debug">Exporting function 'Install-ChocolateyDesktopLink' for backwards compatibility</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="debug">Exporting function 'Write-ChocolateyFailure' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="debug">Exporting function 'Write-ChocolateySuccess' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="debug">Exporting function 'Write-FileUpdateLog' for backwards compatibility</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导入函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导入函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导入函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导入函数“Write-FileUpdateLog”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1”加载模块。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函 数“Remove-Process”。</S><S S="verbose">正在导入函数“Get-AppInstallLocation”。</S><S S="verbose">正在导入函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导入函数“Get-EffectiveProxy”。</S><S S="verbose">正在导入函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导入函数“Get-WebContent”。</S><S S="verbose">正在导入函数“Register-Application”。</S><S S="verbose">正在导入函数“Remove-Process”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1”加载模块。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导入函数“Install-WindowsUpdate”。</S><S S="verbose">正在导入函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在 导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数“Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函数“Remove-Process”。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet “Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正 在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正 在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名 “Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S></Objs>
0
#< CLIXML
<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块 。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><S S="debug">Host version is 5.1.19041.1237, PowerShell Version is '5.1.19041.1237' and CLR Version is '4.0.30319.42000'.</S><S S="verbose"> 正在从路径“C:\ProgramData\chocolatey\helpers\Chocolatey.PowerShell.dll”加载模块。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导入 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导 入 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose"> 正在导入 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导入 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导入 cmdlet“Update-SessionEnvironment”。</S><S S="debug">Cmdlets exported from Chocolatey.PowerShell.dll</S><S S="debug">Get-EnvironmentVariable</S><S S="debug">Get-EnvironmentVariableNames</S><S S="debug">Install-ChocolateyPath</S><S S="debug">Set-EnvironmentVariable</S><S S="debug">Test-ProcessAdminRights</S><S S="debug">Uninstall-ChocolateyPath</S><S S="debug">Update-SessionEnvironment</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在 导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数 “Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正 在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S><S S="debug">Loading community extensions</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-compatibility\chocolatey-compatibility.psm1”加载模块。</S><S S="debug">Function 'Get-PackageParameters' exists, ignoring export.</S><S S="debug">Function 'Get-UninstallRegistryKey' exists, ignoring export.</S><S S="debug">Exporting function 'Install-ChocolateyDesktopLink' for backwards compatibility</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="debug">Exporting function 'Write-ChocolateyFailure' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateyFailure”。</S><S S="debug">Exporting function 'Write-ChocolateySuccess' for backwards compatibility</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="debug">Exporting function 'Write-FileUpdateLog' for backwards compatibility</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导入函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导入函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导入函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导入函数“Write-FileUpdateLog”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-core\chocolatey-core.psm1”加载模块。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函 数“Remove-Process”。</S><S S="verbose">正在导入函数“Get-AppInstallLocation”。</S><S S="verbose">正在导入函数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导入函数“Get-EffectiveProxy”。</S><S S="verbose">正在导入函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导入函数MySQL 服务无法启动。
服务没有报告任何错误。
请键入 NET HELPMSG 3534 以获得更多的帮助。
Microsoft.PowerShell.Commands.WriteErrorException
Microsoft.PowerShell.Commands.WriteErrorException
Microsoft.PowerShell.Commands.WriteErrorException
“Get-WebContent”。</S><S S="verbose">正在导入函数“Register-Application”。</S><S S="verbose">正在导入函数“Remove-Process”。</S><S S="debug">Importing 'C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1'</S><S S="verbose">正在从路径“C:\ProgramData\chocolatey\extensions\chocolatey-windowsupdate\chocolatey-windowsupdate.psm1”加载模块。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导入函数“Install-WindowsUpdate”。</S><S S="verbose">正在导入函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出函数“Format-FileSize”。</S><S S="verbose">正在导出函数“Get-ChecksumValid”。</S><S S="verbose">正在导出函数“Get-ChocolateyConfigValue”。</S><S S="verbose">正在导出函数“Get-ChocolateyPath”。</S><S S="verbose">正在导出函数“Get-ChocolateyUnzip”。</S><S S="verbose">正在导出函数“Get-ChocolateyWebFile”。</S><S S="verbose">正在导出函数“Get-FtpFile”。</S><S S="verbose">正在导出函数“Get-OSArchitectureWidth”。</S><S S="verbose">正在导出函数“Get-PackageParameters”。</S><S S="verbose">正在导出函数“Get-PackageParametersBuiltIn”。</S><S S="verbose">正在 导出函数“Get-ToolsLocation”。</S><S S="verbose">正在导出函数“Get-UACEnabled”。</S><S S="verbose">正在导出函数“Get-UninstallRegistryKey”。</S><S S="verbose">正在导出函数“Get-VirusCheckValid”。</S><S S="verbose">正在导出函数“Get-WebFile”。</S><S S="verbose">正在导出函数“Get-WebFileName”。</S><S S="verbose">正在导出函数“Get-WebHeaders”。</S><S S="verbose">正在导出函数“Install-BinFile”。</S><S S="verbose">正在导出函数“Install-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Install-ChocolateyExplorerMenuItem”。</S><S S="verbose">正在导出函数“Install-ChocolateyFileAssociation”。</S><S S="verbose">正在导出函数“Install-ChocolateyInstallPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyPinnedTaskBarItem”。</S><S S="verbose"> 正在导出函数“Install-ChocolateyPowershellCommand”。</S><S S="verbose">正在导出函数“Install-ChocolateyShortcut”。</S><S S="verbose">正在导出函数“Install-ChocolateyVsixPackage”。</S><S S="verbose">正在导出函数“Install-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Install-Vsix”。</S><S S="verbose">正在导出函数“Set-PowerShellExitCode”。</S><S S="verbose">正在导出函数“Start-ChocolateyProcessAsAdmin”。</S><S S="verbose">正在导出函数“Uninstall-BinFile”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyEnvironmentVariable”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyPackage”。</S><S S="verbose">正在导出函数“Uninstall-ChocolateyZipPackage”。</S><S S="verbose">正在导出函数“Write-FunctionCallLogMessage”。</S><S S="verbose">正在导出函数“Install-ChocolateyDesktopLink”。</S><S S="verbose">正在导 出函数“Write-ChocolateyFailure”。</S><S S="verbose">正在导出函数“Write-ChocolateySuccess”。</S><S S="verbose">正在导出函数“Write-FileUpdateLog”。</S><S S="verbose">正在导出函数“Get-AppInstallLocation”。</S><S S="verbose">正在导出函 数“Get-AvailableDriveLetter”。</S><S S="verbose">正在导出函数“Get-EffectiveProxy”。</S><S S="verbose">正在导出函数“Get-PackageCacheLocation”。</S><S S="verbose">正在导出函数“Get-WebContent”。</S><S S="verbose">正在导出函数“Register-Application”。</S><S S="verbose">正在导出函数“Remove-Process”。</S><S S="verbose">正在导出函数“Install-WindowsUpdate”。</S><S S="verbose">正在导出函数“Test-WindowsUpdate”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable ”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Get-EnvironmentVariableNames”。</S><S S="verbose">正在导出 cmdlet“Install-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Set-EnvironmentVariable”。</S><S S="verbose">正在导出 cmdlet“Test-ProcessAdminRights”。</S><S S="verbose">正在导出 cmdlet“Uninstall-ChocolateyPath”。</S><S S="verbose">正在导出 cmdlet“Update-SessionEnvironment”。</S><S S="verbose">正在导出别名“Get-ProcessorBits”。</S><S S="verbose">正在导出别名“Get-OSBitness”。</S><S S="verbose">正在导出别名“Get-InstallRegistryKey”。</S><S S="verbose">正在导出别名“Generate-BinFile”。</S><S S="verbose">正在导出别名“Add-BinFile”。</S><S S="verbose">正在导出别名“Start-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Invoke-ChocolateyProcess”。</S><S S="verbose">正在导出别名“Remove-BinFile”。</S><S S="verbose">正在导出别名“refreshenv”。</S></Objs>
0
Only an exit code of non-zero will fail the package by default. Set
 `--failonstderr` if you want error messages to also fail a script. See
 `choco -h` for details.
Environment Vars (like PATH) have changed. Close/reopen your shell to
 see the changes (or in powershell/cmd.exe just type `refreshenv`).
 The install of mysql was successful.
  Deployed to 'C:\tools\mysql'

Chocolatey installed 9/9 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Installed:
 - chocolatey-windowsupdate.extension v1.0.5
 - ********* v1.0.20160915
 - ********* v1.0.20160915
 - ********* v1.0.20181019
 - ********* v1.0.5
 - ********* v1.0.3
 - mysql v9.2.0
 - vcredist140 v14.44.35208
 - vcredist2015 v14.0.24215.20170201
PS C:\Windows\system32> choco install redis-64 -y
Chocolatey v2.4.1
Installing the following packages:
redis-64
By installing, you accept licenses for the packages.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading memurai-developer.install 4.1.4... 100%

memurai-developer.install v4.1.4 [Approved]
memurai-developer.install package files install completed. Performing other installation steps.
C:\ProgramData\chocolatey\lib\memurai-developer.install\tools\Memurai-Developer.msi
Installing Memurai Developer (Install)...
Memurai Developer (Install) has been installed.
  memurai-developer.install may be able to be automatically uninstalled.
Environment Vars (like PATH) have changed. Close/reopen your shell to
 see the changes (or in powershell/cmd.exe just type `refreshenv`).
 The install of memurai-developer.install was successful.
  Software installed as 'msi', install location is likely default.
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading memurai-developer 4.1.4... 100%

memurai-developer v4.1.4 [Approved]
memurai-developer package files install completed. Performing other installation steps.
 The install of memurai-developer was successful.
  Deployed to 'C:\ProgramData\chocolatey\lib\memurai-developer'
Downloading package from source 'https://community.chocolatey.org/api/v2/'
Progress: Downloading redis-64 3.1.0... 100%

redis-64 v3.1.0 [Approved]
redis-64 package files install completed. Performing other installation steps.
 The install of redis-64 was successful.
  Deployed to 'C:\ProgramData\chocolatey\lib\redis-64'

Chocolatey installed 3/3 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).
PS C:\Windows\system32>