package edu.tsinghua.studentmis.student.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import edu.tsinghua.studentmis.student.entity.Student;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 学生更新请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "学生更新请求")
public class StudentUpdateRequest {

    @Schema(description = "学号", example = "2024001001")
    @Pattern(regexp = "^[0-9]{8,12}$", message = "学号格式不正确")
    private String studentId;

    @Schema(description = "姓名", example = "张三")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String name;

    @Schema(description = "英文姓名", example = "Zhang San")
    @Size(max = 100, message = "英文姓名长度不能超过100个字符")
    private String nameEn;

    @Schema(description = "性别", example = "MALE")
    private Student.Gender gender;

    @Schema(description = "出生日期", example = "2000-01-01")
    @Past(message = "出生日期必须是过去的日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    @Schema(description = "身份证号", example = "110101200001011234")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;

    @Schema(description = "国籍", example = "中国")
    @Size(max = 50, message = "国籍长度不能超过50个字符")
    private String nationality;

    @Schema(description = "民族", example = "汉族")
    @Size(max = 20, message = "民族长度不能超过20个字符")
    private String ethnicity;

    @Schema(description = "政治面貌", example = "共青团员")
    @Size(max = 20, message = "政治面貌长度不能超过20个字符")
    private String politicalStatus;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "紧急联系人", example = "张父")
    @Size(max = 50, message = "紧急联系人姓名长度不能超过50个字符")
    private String emergencyContact;

    @Schema(description = "紧急联系电话", example = "13900139000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系电话格式不正确")
    private String emergencyPhone;

    @Schema(description = "入学日期", example = "2024-09-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate admissionDate;

    @Schema(description = "毕业日期", example = "2028-06-30")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate graduationDate;

    @Schema(description = "学籍状态", example = "ACTIVE")
    private Student.StudentStatus status;

    @Schema(description = "专业ID", example = "1")
    private Long majorId;

    @Schema(description = "班级ID", example = "1")
    private Long classId;

    @Schema(description = "宿舍", example = "紫荆1号楼101")
    @Size(max = 50, message = "宿舍信息长度不能超过50个字符")
    private String dormitory;

    @Schema(description = "照片URL")
    private String photoUrl;

    @Schema(description = "备注")
    private String remarks;
}
