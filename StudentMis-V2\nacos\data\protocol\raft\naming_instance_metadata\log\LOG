2025/06/19-19:26:11.698041 46bc RocksDB version: 7.7.3
2025/06/19-19:26:11.698141 46bc Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-19:26:11.698162 46bc Compile date 2022-10-24 17:17:55
2025/06/19-19:26:11.698183 46bc DB SUMMARY
2025/06/19-19:26:11.698197 46bc DB Session ID:  K4XFAHQXE6EOWWNXS3EV
2025/06/19-19:26:11.698673 46bc SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log dir, Total Num: 0, files: 
2025/06/19-19:26:11.698696 46bc Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log: 
2025/06/19-19:26:11.698711 46bc                         Options.error_if_exists: 0
2025/06/19-19:26:11.698880 46bc                       Options.create_if_missing: 1
2025/06/19-19:26:11.698889 46bc                         Options.paranoid_checks: 1
2025/06/19-19:26:11.698894 46bc             Options.flush_verify_memtable_count: 1
2025/06/19-19:26:11.698900 46bc                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-19:26:11.698906 46bc        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-19:26:11.698911 46bc                                     Options.env: 000001366FB452F0
2025/06/19-19:26:11.698917 46bc                                      Options.fs: WinFS
2025/06/19-19:26:11.698942 46bc                                Options.info_log: 000001366F731DF0
2025/06/19-19:26:11.698961 46bc                Options.max_file_opening_threads: 16
2025/06/19-19:26:11.698967 46bc                              Options.statistics: 000001366FD925C0
2025/06/19-19:26:11.698972 46bc                               Options.use_fsync: 0
2025/06/19-19:26:11.698977 46bc                       Options.max_log_file_size: 0
2025/06/19-19:26:11.698982 46bc                  Options.max_manifest_file_size: 1073741824
2025/06/19-19:26:11.698987 46bc                   Options.log_file_time_to_roll: 0
2025/06/19-19:26:11.698991 46bc                       Options.keep_log_file_num: 100
2025/06/19-19:26:11.698995 46bc                    Options.recycle_log_file_num: 0
2025/06/19-19:26:11.699000 46bc                         Options.allow_fallocate: 1
2025/06/19-19:26:11.699005 46bc                        Options.allow_mmap_reads: 0
2025/06/19-19:26:11.699009 46bc                       Options.allow_mmap_writes: 0
2025/06/19-19:26:11.699014 46bc                        Options.use_direct_reads: 0
2025/06/19-19:26:11.699020 46bc                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-19:26:11.699025 46bc          Options.create_missing_column_families: 1
2025/06/19-19:26:11.699030 46bc                              Options.db_log_dir: 
2025/06/19-19:26:11.699036 46bc                                 Options.wal_dir: 
2025/06/19-19:26:11.699041 46bc                Options.table_cache_numshardbits: 6
2025/06/19-19:26:11.699045 46bc                         Options.WAL_ttl_seconds: 0
2025/06/19-19:26:11.699050 46bc                       Options.WAL_size_limit_MB: 0
2025/06/19-19:26:11.699056 46bc                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-19:26:11.699060 46bc             Options.manifest_preallocation_size: 4194304
2025/06/19-19:26:11.699065 46bc                     Options.is_fd_close_on_exec: 1
2025/06/19-19:26:11.699070 46bc                   Options.advise_random_on_open: 1
2025/06/19-19:26:11.699075 46bc                    Options.db_write_buffer_size: 0
2025/06/19-19:26:11.699080 46bc                    Options.write_buffer_manager: 000001366FB444E0
2025/06/19-19:26:11.699084 46bc         Options.access_hint_on_compaction_start: 1
2025/06/19-19:26:11.699089 46bc           Options.random_access_max_buffer_size: 1048576
2025/06/19-19:26:11.699095 46bc                      Options.use_adaptive_mutex: 0
2025/06/19-19:26:11.699100 46bc                            Options.rate_limiter: 0000000000000000
2025/06/19-19:26:11.699106 46bc     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-19:26:11.699111 46bc                       Options.wal_recovery_mode: 2
2025/06/19-19:26:11.699181 46bc                  Options.enable_thread_tracking: 0
2025/06/19-19:26:11.699190 46bc                  Options.enable_pipelined_write: 0
2025/06/19-19:26:11.699196 46bc                  Options.unordered_write: 0
2025/06/19-19:26:11.699201 46bc         Options.allow_concurrent_memtable_write: 1
2025/06/19-19:26:11.699206 46bc      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-19:26:11.699211 46bc             Options.write_thread_max_yield_usec: 100
2025/06/19-19:26:11.699217 46bc            Options.write_thread_slow_yield_usec: 3
2025/06/19-19:26:11.699222 46bc                               Options.row_cache: None
2025/06/19-19:26:11.699227 46bc                              Options.wal_filter: None
2025/06/19-19:26:11.699233 46bc             Options.avoid_flush_during_recovery: 0
2025/06/19-19:26:11.699238 46bc             Options.allow_ingest_behind: 0
2025/06/19-19:26:11.699243 46bc             Options.two_write_queues: 0
2025/06/19-19:26:11.699248 46bc             Options.manual_wal_flush: 0
2025/06/19-19:26:11.699253 46bc             Options.wal_compression: 0
2025/06/19-19:26:11.699258 46bc             Options.atomic_flush: 0
2025/06/19-19:26:11.699263 46bc             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-19:26:11.699268 46bc                 Options.persist_stats_to_disk: 0
2025/06/19-19:26:11.699273 46bc                 Options.write_dbid_to_manifest: 0
2025/06/19-19:26:11.699277 46bc                 Options.log_readahead_size: 0
2025/06/19-19:26:11.699282 46bc                 Options.file_checksum_gen_factory: Unknown
2025/06/19-19:26:11.699287 46bc                 Options.best_efforts_recovery: 0
2025/06/19-19:26:11.699293 46bc                Options.max_bgerror_resume_count: 2147483647
2025/06/19-19:26:11.699298 46bc            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-19:26:11.699303 46bc             Options.allow_data_in_errors: 0
2025/06/19-19:26:11.699308 46bc             Options.db_host_id: __hostname__
2025/06/19-19:26:11.699312 46bc             Options.enforce_single_del_contracts: true
2025/06/19-19:26:11.699318 46bc             Options.max_background_jobs: 2
2025/06/19-19:26:11.699322 46bc             Options.max_background_compactions: 4
2025/06/19-19:26:11.699327 46bc             Options.max_subcompactions: 1
2025/06/19-19:26:11.699332 46bc             Options.avoid_flush_during_shutdown: 0
2025/06/19-19:26:11.699337 46bc           Options.writable_file_max_buffer_size: 1048576
2025/06/19-19:26:11.699343 46bc             Options.delayed_write_rate : 16777216
2025/06/19-19:26:11.699348 46bc             Options.max_total_wal_size: 1073741824
2025/06/19-19:26:11.699353 46bc             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-19:26:11.699358 46bc                   Options.stats_dump_period_sec: 600
2025/06/19-19:26:11.699363 46bc                 Options.stats_persist_period_sec: 600
2025/06/19-19:26:11.699368 46bc                 Options.stats_history_buffer_size: 1048576
2025/06/19-19:26:11.699373 46bc                          Options.max_open_files: -1
2025/06/19-19:26:11.699379 46bc                          Options.bytes_per_sync: 0
2025/06/19-19:26:11.699383 46bc                      Options.wal_bytes_per_sync: 0
2025/06/19-19:26:11.699389 46bc                   Options.strict_bytes_per_sync: 0
2025/06/19-19:26:11.699395 46bc       Options.compaction_readahead_size: 0
2025/06/19-19:26:11.699400 46bc                  Options.max_background_flushes: 1
2025/06/19-19:26:11.699406 46bc Compression algorithms supported:
2025/06/19-19:26:11.699411 46bc 	kZSTD supported: 1
2025/06/19-19:26:11.699417 46bc 	kSnappyCompression supported: 1
2025/06/19-19:26:11.699423 46bc 	kBZip2Compression supported: 0
2025/06/19-19:26:11.699428 46bc 	kZlibCompression supported: 1
2025/06/19-19:26:11.699433 46bc 	kLZ4Compression supported: 1
2025/06/19-19:26:11.699437 46bc 	kXpressCompression supported: 0
2025/06/19-19:26:11.699442 46bc 	kLZ4HCCompression supported: 1
2025/06/19-19:26:11.699486 46bc 	kZSTDNotFinalCompression supported: 1
2025/06/19-19:26:11.699496 46bc Fast CRC32 supported: Not supported on x86
2025/06/19-19:26:11.699502 46bc DMutex implementation: std::mutex
2025/06/19-19:26:11.730463 46bc [db\db_impl\db_impl_open.cc:313] Creating manifest 1 
2025/06/19-19:26:11.734007 46bc [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000001
2025/06/19-19:26:11.734253 46bc [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-19:26:11.734262 46bc               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:26:11.734267 46bc           Options.merge_operator: StringAppendOperator
2025/06/19-19:26:11.734270 46bc        Options.compaction_filter: None
2025/06/19-19:26:11.734274 46bc        Options.compaction_filter_factory: None
2025/06/19-19:26:11.734277 46bc  Options.sst_partitioner_factory: None
2025/06/19-19:26:11.734281 46bc         Options.memtable_factory: SkipListFactory
2025/06/19-19:26:11.734284 46bc            Options.table_factory: BlockBasedTable
2025/06/19-19:26:11.734324 46bc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001366F26A890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000013670652E30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:26:11.734330 46bc        Options.write_buffer_size: 67108864
2025/06/19-19:26:11.734334 46bc  Options.max_write_buffer_number: 3
2025/06/19-19:26:11.734339 46bc          Options.compression: Snappy
2025/06/19-19:26:11.734342 46bc                  Options.bottommost_compression: Disabled
2025/06/19-19:26:11.734346 46bc       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:26:11.734350 46bc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:26:11.734353 46bc             Options.num_levels: 7
2025/06/19-19:26:11.734357 46bc        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:26:11.734360 46bc     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:26:11.734364 46bc     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:26:11.734368 46bc            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:26:11.734371 46bc                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:26:11.734375 46bc               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:26:11.734379 46bc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.734382 46bc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.734387 46bc         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:26:11.734392 46bc                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:26:11.734397 46bc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.734401 46bc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.734471 46bc            Options.compression_opts.window_bits: -14
2025/06/19-19:26:11.734479 46bc                  Options.compression_opts.level: 32767
2025/06/19-19:26:11.734482 46bc               Options.compression_opts.strategy: 0
2025/06/19-19:26:11.734486 46bc         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.734490 46bc         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.734493 46bc         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.734497 46bc         Options.compression_opts.parallel_threads: 1
2025/06/19-19:26:11.734500 46bc                  Options.compression_opts.enabled: false
2025/06/19-19:26:11.734504 46bc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.734507 46bc      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:26:11.734511 46bc          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:26:11.734514 46bc              Options.level0_stop_writes_trigger: 40
2025/06/19-19:26:11.734518 46bc                   Options.target_file_size_base: 67108864
2025/06/19-19:26:11.734521 46bc             Options.target_file_size_multiplier: 1
2025/06/19-19:26:11.734525 46bc                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:26:11.734528 46bc Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:26:11.734532 46bc          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:26:11.734536 46bc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:26:11.734540 46bc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:26:11.734544 46bc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:26:11.734547 46bc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:26:11.734550 46bc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:26:11.734554 46bc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:26:11.734557 46bc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:26:11.734561 46bc       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:26:11.734564 46bc                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:26:11.734568 46bc                        Options.arena_block_size: 1048576
2025/06/19-19:26:11.734572 46bc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:26:11.734575 46bc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:26:11.734579 46bc                Options.disable_auto_compactions: 0
2025/06/19-19:26:11.734583 46bc                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:26:11.734588 46bc                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:26:11.734591 46bc Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:26:11.734595 46bc Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:26:11.734598 46bc Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:26:11.734602 46bc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:26:11.734606 46bc Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:26:11.734610 46bc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:26:11.734613 46bc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:26:11.734617 46bc Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:26:11.734623 46bc                   Options.table_properties_collectors: 
2025/06/19-19:26:11.734626 46bc                   Options.inplace_update_support: 0
2025/06/19-19:26:11.734630 46bc                 Options.inplace_update_num_locks: 10000
2025/06/19-19:26:11.734633 46bc               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:26:11.734637 46bc               Options.memtable_whole_key_filtering: 0
2025/06/19-19:26:11.734641 46bc   Options.memtable_huge_page_size: 0
2025/06/19-19:26:11.734646 46bc                           Options.bloom_locality: 0
2025/06/19-19:26:11.734650 46bc                    Options.max_successive_merges: 0
2025/06/19-19:26:11.734653 46bc                Options.optimize_filters_for_hits: 0
2025/06/19-19:26:11.734657 46bc                Options.paranoid_file_checks: 0
2025/06/19-19:26:11.734660 46bc                Options.force_consistency_checks: 1
2025/06/19-19:26:11.734664 46bc                Options.report_bg_io_stats: 0
2025/06/19-19:26:11.734667 46bc                               Options.ttl: 2592000
2025/06/19-19:26:11.734670 46bc          Options.periodic_compaction_seconds: 0
2025/06/19-19:26:11.734674 46bc  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:26:11.734677 46bc                       Options.enable_blob_files: false
2025/06/19-19:26:11.734681 46bc                           Options.min_blob_size: 0
2025/06/19-19:26:11.734684 46bc                          Options.blob_file_size: 268435456
2025/06/19-19:26:11.734688 46bc                   Options.blob_compression_type: NoCompression
2025/06/19-19:26:11.734692 46bc          Options.enable_blob_garbage_collection: false
2025/06/19-19:26:11.734695 46bc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:26:11.734699 46bc Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:26:11.734703 46bc          Options.blob_compaction_readahead_size: 0
2025/06/19-19:26:11.734706 46bc                Options.blob_file_starting_level: 0
2025/06/19-19:26:11.734710 46bc Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:26:11.738138 46bc [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/06/19-19:26:11.738156 46bc [db\version_set.cc:5588] Column family [default] (ID 0), log number is 0
2025/06/19-19:26:11.738759 46bc [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9e-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-19:26:11.740530 46bc [db\version_set.cc:5051] Creating manifest 5
2025/06/19-19:26:11.744567 46bc [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-19:26:11.744582 46bc               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:26:11.744586 46bc           Options.merge_operator: StringAppendOperator
2025/06/19-19:26:11.744589 46bc        Options.compaction_filter: None
2025/06/19-19:26:11.744593 46bc        Options.compaction_filter_factory: None
2025/06/19-19:26:11.744596 46bc  Options.sst_partitioner_factory: None
2025/06/19-19:26:11.744599 46bc         Options.memtable_factory: SkipListFactory
2025/06/19-19:26:11.744602 46bc            Options.table_factory: BlockBasedTable
2025/06/19-19:26:11.744641 46bc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001366F26A890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000013670652E30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:26:11.744650 46bc        Options.write_buffer_size: 67108864
2025/06/19-19:26:11.744654 46bc  Options.max_write_buffer_number: 3
2025/06/19-19:26:11.744658 46bc          Options.compression: Snappy
2025/06/19-19:26:11.744662 46bc                  Options.bottommost_compression: Disabled
2025/06/19-19:26:11.744665 46bc       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:26:11.744669 46bc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:26:11.744672 46bc             Options.num_levels: 7
2025/06/19-19:26:11.744676 46bc        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:26:11.744679 46bc     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:26:11.744682 46bc     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:26:11.744686 46bc            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:26:11.744689 46bc                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:26:11.744693 46bc               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:26:11.744696 46bc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.744700 46bc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.744703 46bc         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:26:11.744707 46bc                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:26:11.744710 46bc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.744714 46bc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.744717 46bc            Options.compression_opts.window_bits: -14
2025/06/19-19:26:11.744721 46bc                  Options.compression_opts.level: 32767
2025/06/19-19:26:11.744724 46bc               Options.compression_opts.strategy: 0
2025/06/19-19:26:11.744728 46bc         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.744731 46bc         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.744735 46bc         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.744738 46bc         Options.compression_opts.parallel_threads: 1
2025/06/19-19:26:11.744741 46bc                  Options.compression_opts.enabled: false
2025/06/19-19:26:11.744745 46bc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.744748 46bc      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:26:11.744752 46bc          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:26:11.744755 46bc              Options.level0_stop_writes_trigger: 40
2025/06/19-19:26:11.744758 46bc                   Options.target_file_size_base: 67108864
2025/06/19-19:26:11.744761 46bc             Options.target_file_size_multiplier: 1
2025/06/19-19:26:11.744765 46bc                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:26:11.744768 46bc Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:26:11.744771 46bc          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:26:11.744776 46bc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:26:11.744779 46bc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:26:11.744782 46bc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:26:11.744786 46bc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:26:11.744789 46bc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:26:11.744792 46bc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:26:11.744795 46bc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:26:11.744799 46bc       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:26:11.744802 46bc                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:26:11.744805 46bc                        Options.arena_block_size: 1048576
2025/06/19-19:26:11.744810 46bc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:26:11.744814 46bc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:26:11.744817 46bc                Options.disable_auto_compactions: 0
2025/06/19-19:26:11.744823 46bc                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:26:11.744830 46bc                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:26:11.744833 46bc Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:26:11.744836 46bc Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:26:11.744839 46bc Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:26:11.744843 46bc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:26:11.744846 46bc Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:26:11.744851 46bc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:26:11.744854 46bc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:26:11.744857 46bc Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:26:11.744863 46bc                   Options.table_properties_collectors: 
2025/06/19-19:26:11.744866 46bc                   Options.inplace_update_support: 0
2025/06/19-19:26:11.744870 46bc                 Options.inplace_update_num_locks: 10000
2025/06/19-19:26:11.744873 46bc               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:26:11.744877 46bc               Options.memtable_whole_key_filtering: 0
2025/06/19-19:26:11.744880 46bc   Options.memtable_huge_page_size: 0
2025/06/19-19:26:11.744883 46bc                           Options.bloom_locality: 0
2025/06/19-19:26:11.744887 46bc                    Options.max_successive_merges: 0
2025/06/19-19:26:11.744890 46bc                Options.optimize_filters_for_hits: 0
2025/06/19-19:26:11.744893 46bc                Options.paranoid_file_checks: 0
2025/06/19-19:26:11.744896 46bc                Options.force_consistency_checks: 1
2025/06/19-19:26:11.744900 46bc                Options.report_bg_io_stats: 0
2025/06/19-19:26:11.744903 46bc                               Options.ttl: 2592000
2025/06/19-19:26:11.744906 46bc          Options.periodic_compaction_seconds: 0
2025/06/19-19:26:11.744909 46bc  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:26:11.744912 46bc                       Options.enable_blob_files: false
2025/06/19-19:26:11.744916 46bc                           Options.min_blob_size: 0
2025/06/19-19:26:11.744919 46bc                          Options.blob_file_size: 268435456
2025/06/19-19:26:11.744922 46bc                   Options.blob_compression_type: NoCompression
2025/06/19-19:26:11.744926 46bc          Options.enable_blob_garbage_collection: false
2025/06/19-19:26:11.744929 46bc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:26:11.744933 46bc Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:26:11.744937 46bc          Options.blob_compaction_readahead_size: 0
2025/06/19-19:26:11.744940 46bc                Options.blob_file_starting_level: 0
2025/06/19-19:26:11.744943 46bc Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:26:11.747283 46bc [db\db_impl\db_impl.cc:3086] Created column family [Configuration] (ID 1)
2025/06/19-19:26:11.756414 46bc [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000001366D516540
2025/06/19-19:26:11.756929 46bc DB pointer 000001367065DB80
