package edu.tsinghua.studentmis.analytics.dto;

import edu.tsinghua.studentmis.analytics.entity.GradePrediction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 成绩预测结果DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Builder
@Schema(description = "成绩预测结果")
public class GradePredictionResult {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "课程安排ID")
    private Long scheduleId;

    @Schema(description = "预测类型")
    private GradePrediction.PredictionType predictionType;

    @Schema(description = "预测分数")
    private BigDecimal predictedScore;

    @Schema(description = "置信度")
    private BigDecimal confidenceLevel;

    @Schema(description = "模型版本")
    private String modelVersion;

    @Schema(description = "预测因子")
    private Map<String, Object> predictionFactors;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "建议")
    private String recommendation;
}
