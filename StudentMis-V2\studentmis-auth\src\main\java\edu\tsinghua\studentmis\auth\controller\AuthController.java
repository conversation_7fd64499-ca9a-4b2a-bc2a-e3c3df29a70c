package edu.tsinghua.studentmis.auth.controller;

import cn.hutool.core.util.StrUtil;
import edu.tsinghua.studentmis.auth.dto.LoginRequest;
import edu.tsinghua.studentmis.auth.dto.LoginResponse;
import edu.tsinghua.studentmis.auth.service.AuthService;
import edu.tsinghua.studentmis.common.result.Result;
import edu.tsinghua.studentmis.common.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "认证管理", description = "用户登录、登出、令牌刷新等认证相关接口")
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录", description = "用户名密码登录，返回JWT令牌")
    @PostMapping("/login")
    public Result<LoginResponse> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        String clientIp = getClientIp(httpRequest);
        LoginResponse response = authService.login(request, clientIp);
        
        return Result.success("登录成功", response);
    }

    @Operation(summary = "用户登出", description = "用户登出，清除令牌缓存")
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        authService.logout(token);

        return Result.success();
    }

    @Operation(summary = "刷新令牌", description = "使用当前令牌刷新获取新的令牌")
    @PostMapping("/refresh")
    public Result<LoginResponse> refreshToken(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        LoginResponse response = authService.refreshToken(token);
        
        return Result.success("令牌刷新成功", response);
    }

    @Operation(summary = "获取当前用户信息", description = "根据令牌获取当前登录用户的基本信息")
    @GetMapping("/me")
    public Result<LoginResponse> getCurrentUser() {
        Long userId = SecurityUtils.getCurrentUserId();
        String username = SecurityUtils.getCurrentUsername();
        String realName = SecurityUtils.getCurrentRealName();
        
        LoginResponse response = LoginResponse.builder()
                .userId(userId)
                .username(username)
                .realName(realName)
                .build();
        
        return Result.success("获取用户信息成功", response);
    }

    @Operation(summary = "验证令牌", description = "验证JWT令牌是否有效")
    @PostMapping("/validate")
    public Result<Boolean> validateToken(
            @Parameter(description = "JWT令牌") @RequestParam String token) {
        
        try {
            boolean isValid = !SecurityUtils.isTokenExpired(token);
            return Result.success("令牌验证完成", isValid);
        } catch (Exception e) {
            return Result.success("令牌验证完成", false);
        }
    }

    /**
     * 从请求中获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (StrUtil.isNotBlank(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * 从请求中获取令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}
