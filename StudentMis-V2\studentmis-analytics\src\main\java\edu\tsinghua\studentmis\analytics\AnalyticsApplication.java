package edu.tsinghua.studentmis.analytics;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 数据分析服务启动类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@SpringBootApplication(scanBasePackages = {"edu.tsinghua.studentmis"})
@EnableDiscoveryClient
@EnableAsync
@EnableScheduling
@MapperScan("edu.tsinghua.studentmis.analytics.mapper")
public class AnalyticsApplication {

    public static void main(String[] args) {
        SpringApplication.run(AnalyticsApplication.class, args);
        System.out.println("============================================");
        System.out.println("  StudentMIS V2 Analytics Service Started  ");
        System.out.println("  清华大学级学生成绩管理系统 - 数据分析服务  ");
        System.out.println("============================================");
    }
}
