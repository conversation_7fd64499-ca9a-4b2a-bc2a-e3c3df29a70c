package edu.tsinghua.studentmis.analytics.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import edu.tsinghua.studentmis.analytics.dto.GradePredictionRequest;
import edu.tsinghua.studentmis.analytics.dto.GradePredictionResult;
import edu.tsinghua.studentmis.analytics.entity.GradePrediction;
import edu.tsinghua.studentmis.analytics.mapper.GradePredictionMapper;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.regression.SimpleRegression;
import org.springframework.stereotype.Service;
import weka.classifiers.functions.LinearRegression;
import weka.core.Attribute;
import weka.core.DenseInstance;
import weka.core.Instance;
import weka.core.Instances;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 成绩预测服务
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GradePredictionService {

    private final GradePredictionMapper gradePredictionMapper;
    private final LearningBehaviorAnalysisService learningBehaviorService;

    /**
     * 预测学生成绩
     */
    public GradePredictionResult predictGrade(GradePredictionRequest request) {
        try {
            log.info("开始预测学生成绩: studentId={}, scheduleId={}", 
                    request.getStudentId(), request.getScheduleId());

            // 获取历史数据
            List<Map<String, Object>> historicalData = getHistoricalData(
                    request.getStudentId(), request.getScheduleId());

            if (CollUtil.isEmpty(historicalData)) {
                throw new BusinessException(ResultCode.ANALYTICS_DATA_ERROR, "历史数据不足，无法进行预测");
            }

            // 选择预测模型
            GradePredictionResult result;
            switch (request.getPredictionType()) {
                case MIDTERM:
                    result = predictMidtermGrade(request, historicalData);
                    break;
                case FINAL:
                    result = predictFinalGrade(request, historicalData);
                    break;
                case TOTAL:
                    result = predictTotalGrade(request, historicalData);
                    break;
                default:
                    throw new BusinessException(ResultCode.BAD_REQUEST, "不支持的预测类型");
            }

            // 保存预测结果
            savePredictionResult(request, result);

            log.info("成绩预测完成: studentId={}, predictedScore={}, confidence={}", 
                    request.getStudentId(), result.getPredictedScore(), result.getConfidenceLevel());

            return result;

        } catch (Exception e) {
            log.error("成绩预测失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.PREDICTION_MODEL_ERROR, "成绩预测失败: " + e.getMessage());
        }
    }

    /**
     * 预测期中成绩
     */
    private GradePredictionResult predictMidtermGrade(GradePredictionRequest request, 
                                                     List<Map<String, Object>> historicalData) {
        // 使用线性回归模型预测期中成绩
        SimpleRegression regression = new SimpleRegression();

        // 准备训练数据：平时成绩 -> 期中成绩
        for (Map<String, Object> data : historicalData) {
            Double usualScore = (Double) data.get("usual_score");
            Double midtermScore = (Double) data.get("midterm_score");
            
            if (usualScore != null && midtermScore != null) {
                regression.addData(usualScore, midtermScore);
            }
        }

        if (regression.getN() < 3) {
            throw new BusinessException(ResultCode.ANALYTICS_DATA_ERROR, "训练数据不足");
        }

        // 获取当前学生的平时成绩
        Double currentUsualScore = getCurrentUsualScore(request.getStudentId(), request.getScheduleId());
        if (currentUsualScore == null) {
            throw new BusinessException(ResultCode.ANALYTICS_DATA_ERROR, "缺少平时成绩数据");
        }

        // 进行预测
        double predictedScore = regression.predict(currentUsualScore);
        double confidence = calculateConfidence(regression.getRSquare());

        // 结合学习行为分析调整预测结果
        double behaviorAdjustment = getBehaviorAdjustment(request.getStudentId());
        predictedScore += behaviorAdjustment;

        // 确保预测分数在合理范围内
        predictedScore = Math.max(0, Math.min(100, predictedScore));

        return GradePredictionResult.builder()
                .studentId(request.getStudentId())
                .scheduleId(request.getScheduleId())
                .predictionType(request.getPredictionType())
                .predictedScore(BigDecimal.valueOf(predictedScore).setScale(2, BigDecimal.ROUND_HALF_UP))
                .confidenceLevel(BigDecimal.valueOf(confidence).setScale(2, BigDecimal.ROUND_HALF_UP))
                .modelVersion("LinearRegression-v1.0")
                .predictionFactors(buildPredictionFactors(regression, currentUsualScore, behaviorAdjustment))
                .build();
    }

    /**
     * 预测期末成绩
     */
    private GradePredictionResult predictFinalGrade(GradePredictionRequest request, 
                                                   List<Map<String, Object>> historicalData) {
        try {
            // 使用Weka的线性回归模型
            Instances dataset = buildWekaDataset(historicalData, "final");
            
            LinearRegression model = new LinearRegression();
            model.buildClassifier(dataset);

            // 准备预测实例
            Instance predictionInstance = buildPredictionInstance(request, dataset);
            
            // 进行预测
            double predictedScore = model.classifyInstance(predictionInstance);
            
            // 计算置信度
            double confidence = calculateWekaConfidence(model, dataset);

            // 结合学习行为分析
            double behaviorAdjustment = getBehaviorAdjustment(request.getStudentId());
            predictedScore += behaviorAdjustment;

            predictedScore = Math.max(0, Math.min(100, predictedScore));

            return GradePredictionResult.builder()
                    .studentId(request.getStudentId())
                    .scheduleId(request.getScheduleId())
                    .predictionType(request.getPredictionType())
                    .predictedScore(BigDecimal.valueOf(predictedScore).setScale(2, BigDecimal.ROUND_HALF_UP))
                    .confidenceLevel(BigDecimal.valueOf(confidence).setScale(2, BigDecimal.ROUND_HALF_UP))
                    .modelVersion("WekaLinearRegression-v1.0")
                    .predictionFactors(buildWekaFactors(model, predictionInstance))
                    .build();

        } catch (Exception e) {
            log.error("Weka模型预测失败，回退到简单模型", e);
            return predictWithSimpleModel(request, historicalData, "final");
        }
    }

    /**
     * 预测总成绩
     */
    private GradePredictionResult predictTotalGrade(GradePredictionRequest request, 
                                                   List<Map<String, Object>> historicalData) {
        // 综合多个因素预测总成绩
        Map<String, Double> currentScores = getCurrentScores(request.getStudentId(), request.getScheduleId());
        
        double predictedTotal = 0.0;
        double confidence = 0.0;
        
        if (currentScores.containsKey("usual") && currentScores.containsKey("midterm")) {
            // 如果有平时成绩和期中成绩，预测期末成绩然后计算总成绩
            GradePredictionRequest finalRequest = new GradePredictionRequest();
            finalRequest.setStudentId(request.getStudentId());
            finalRequest.setScheduleId(request.getScheduleId());
            finalRequest.setPredictionType(GradePrediction.PredictionType.FINAL);
            
            GradePredictionResult finalPrediction = predictFinalGrade(finalRequest, historicalData);
            
            // 按权重计算总成绩：平时30% + 期中30% + 期末40%
            predictedTotal = currentScores.get("usual") * 0.3 + 
                           currentScores.get("midterm") * 0.3 + 
                           finalPrediction.getPredictedScore().doubleValue() * 0.4;
            
            confidence = finalPrediction.getConfidenceLevel().doubleValue() * 0.8; // 降低置信度
            
        } else if (currentScores.containsKey("usual")) {
            // 只有平时成绩，预测期中和期末
            return predictWithSimpleModel(request, historicalData, "total");
        } else {
            throw new BusinessException(ResultCode.ANALYTICS_DATA_ERROR, "缺少必要的成绩数据");
        }

        return GradePredictionResult.builder()
                .studentId(request.getStudentId())
                .scheduleId(request.getScheduleId())
                .predictionType(request.getPredictionType())
                .predictedScore(BigDecimal.valueOf(predictedTotal).setScale(2, BigDecimal.ROUND_HALF_UP))
                .confidenceLevel(BigDecimal.valueOf(confidence).setScale(2, BigDecimal.ROUND_HALF_UP))
                .modelVersion("Composite-v1.0")
                .predictionFactors(buildCompositeFactors(currentScores))
                .build();
    }

    /**
     * 获取历史数据
     */
    private List<Map<String, Object>> getHistoricalData(Long studentId, Long scheduleId) {
        // 获取同课程的历史成绩数据
        return gradePredictionMapper.getHistoricalGradeData(scheduleId);
    }

    /**
     * 获取当前平时成绩
     */
    private Double getCurrentUsualScore(Long studentId, Long scheduleId) {
        return gradePredictionMapper.getCurrentUsualScore(studentId, scheduleId);
    }

    /**
     * 获取当前各项成绩
     */
    private Map<String, Double> getCurrentScores(Long studentId, Long scheduleId) {
        Map<String, Object> scores = gradePredictionMapper.getCurrentScores(studentId, scheduleId);
        Map<String, Double> result = new HashMap<>();
        
        if (scores.get("usual_score") != null) {
            result.put("usual", ((Number) scores.get("usual_score")).doubleValue());
        }
        if (scores.get("midterm_score") != null) {
            result.put("midterm", ((Number) scores.get("midterm_score")).doubleValue());
        }
        if (scores.get("final_score") != null) {
            result.put("final", ((Number) scores.get("final_score")).doubleValue());
        }
        
        return result;
    }

    /**
     * 获取学习行为调整因子
     */
    private double getBehaviorAdjustment(Long studentId) {
        try {
            // 获取学习行为分析结果
            var behaviorAnalysis = learningBehaviorService.getLatestBehaviorAnalysis(studentId);
            
            if (behaviorAnalysis == null) {
                return 0.0;
            }
            
            // 根据学习行为计算调整因子
            double adjustment = 0.0;
            
            // 出勤率影响
            if (behaviorAnalysis.getAttendanceRate() != null) {
                double attendanceRate = behaviorAnalysis.getAttendanceRate().doubleValue();
                if (attendanceRate > 0.9) {
                    adjustment += 2.0; // 出勤率高，加分
                } else if (attendanceRate < 0.7) {
                    adjustment -= 3.0; // 出勤率低，减分
                }
            }
            
            // 作业完成率影响
            if (behaviorAnalysis.getAssignmentCompletionRate() != null) {
                double completionRate = behaviorAnalysis.getAssignmentCompletionRate().doubleValue();
                if (completionRate > 0.9) {
                    adjustment += 1.5;
                } else if (completionRate < 0.6) {
                    adjustment -= 2.0;
                }
            }
            
            // 参与度影响
            if ("HIGH".equals(behaviorAnalysis.getEngagementLevel())) {
                adjustment += 1.0;
            } else if ("LOW".equals(behaviorAnalysis.getEngagementLevel())) {
                adjustment -= 1.0;
            }
            
            return Math.max(-5.0, Math.min(5.0, adjustment)); // 限制调整范围
            
        } catch (Exception e) {
            log.warn("获取学习行为调整因子失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算置信度
     */
    private double calculateConfidence(double rSquare) {
        // 基于R²值计算置信度
        if (rSquare > 0.8) {
            return 0.9;
        } else if (rSquare > 0.6) {
            return 0.8;
        } else if (rSquare > 0.4) {
            return 0.7;
        } else {
            return 0.6;
        }
    }

    /**
     * 简单模型预测（回退方案）
     */
    private GradePredictionResult predictWithSimpleModel(GradePredictionRequest request, 
                                                        List<Map<String, Object>> historicalData, 
                                                        String targetField) {
        // 使用历史平均值和趋势分析
        List<Double> scores = historicalData.stream()
                .map(data -> (Double) data.get(targetField + "_score"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (scores.isEmpty()) {
            throw new BusinessException(ResultCode.ANALYTICS_DATA_ERROR, "历史数据不足");
        }

        double average = scores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double predictedScore = average + getBehaviorAdjustment(request.getStudentId());
        
        predictedScore = Math.max(0, Math.min(100, predictedScore));

        return GradePredictionResult.builder()
                .studentId(request.getStudentId())
                .scheduleId(request.getScheduleId())
                .predictionType(request.getPredictionType())
                .predictedScore(BigDecimal.valueOf(predictedScore).setScale(2, BigDecimal.ROUND_HALF_UP))
                .confidenceLevel(BigDecimal.valueOf(0.6))
                .modelVersion("SimpleAverage-v1.0")
                .predictionFactors(Map.of("historical_average", average, "sample_size", scores.size()))
                .build();
    }

    // 其他辅助方法...
    private Instances buildWekaDataset(List<Map<String, Object>> data, String target) {
        // 构建Weka数据集的实现
        return null; // 简化实现
    }

    private Instance buildPredictionInstance(GradePredictionRequest request, Instances dataset) {
        // 构建预测实例的实现
        return null; // 简化实现
    }

    private double calculateWekaConfidence(LinearRegression model, Instances dataset) {
        return 0.75; // 简化实现
    }

    private Map<String, Object> buildPredictionFactors(SimpleRegression regression, 
                                                      Double usualScore, double behaviorAdjustment) {
        Map<String, Object> factors = new HashMap<>();
        factors.put("usual_score", usualScore);
        factors.put("r_square", regression.getRSquare());
        factors.put("behavior_adjustment", behaviorAdjustment);
        factors.put("sample_size", regression.getN());
        return factors;
    }

    private Map<String, Object> buildWekaFactors(LinearRegression model, Instance instance) {
        return Map.of("model_type", "LinearRegression", "features", instance.numAttributes() - 1);
    }

    private Map<String, Object> buildCompositeFactors(Map<String, Double> currentScores) {
        return new HashMap<>(currentScores);
    }

    /**
     * 保存预测结果
     */
    private void savePredictionResult(GradePredictionRequest request, GradePredictionResult result) {
        GradePrediction prediction = new GradePrediction();
        prediction.setStudentId(request.getStudentId());
        prediction.setScheduleId(request.getScheduleId());
        prediction.setPredictionType(request.getPredictionType());
        prediction.setPredictedScore(result.getPredictedScore());
        prediction.setConfidenceLevel(result.getConfidenceLevel());
        prediction.setModelVersion(result.getModelVersion());
        prediction.setPredictionFactors(result.getPredictionFactors().toString());
        prediction.setPredictionDate(LocalDateTime.now());

        gradePredictionMapper.insert(prediction);
    }
}
