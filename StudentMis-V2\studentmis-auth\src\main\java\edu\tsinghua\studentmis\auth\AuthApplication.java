package edu.tsinghua.studentmis.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 认证服务启动类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@SpringBootApplication(scanBasePackages = {"edu.tsinghua.studentmis"})
@EnableDiscoveryClient
@MapperScan("edu.tsinghua.studentmis.auth.mapper")
public class AuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthApplication.class, args);
        System.out.println("========================================");
        System.out.println("  StudentMIS V2 Auth Service Started  ");
        System.out.println("  清华大学级学生成绩管理系统 - 认证服务  ");
        System.out.println("========================================");
    }
}
