<%@ page import="com.ntvu.studentmis.db.DBCourse" %>
<%@ page import="com.ntvu.studentmis.entity.Course" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>修改课程信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>修改课程信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <%
            String id=request.getParameter("id");
            System.out.println(id+":id");
            Course course=new DBCourse().getListById((Integer.parseInt(id)));
        %>
        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请修改<small>课程信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/course/CourseServlet?action=edit"%>" onsubmit="return verify()">
                                <input type="hidden" name="id" value="<%= course.getCourse_id()%>">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label >科目</label>
                                        <input type="text" name="txtName" class="form-control"  value="<%=course.getCourse_name()%>">
                                    </div>
                                    <div class="form-group">
                                        <label >学分</label>
                                        <input type="text" name="txtCredit" class="form-control"  value="<%=course.getCourse_credit()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>学时</label>
                                        <input type="text" name="txtHours" class="form-control"  value="<%=course.getCourse_hours()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>任课教师</label>
                                        <input type="text" name="txtTeacher" class="form-control"  value="<%=course.getCourse_teacher()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>开设时间</label>
                                        <input type="text" name="txtDate" class="form-control" value="<%=course.getCoursedate()%>">
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<!-- ./wrapper -->
<script type="text/javascript">
    function verify()
    {
        console.log(`click`);
        //对数据进行检验
        let txtName=$(`input[name=txtName]`).val();
        if(txtName==='')
        {
            alert(`科目不能为空`);
            $(`input[name=txtName]`).focus();//光标选中
            return false;
        }
        let txtCredit=$(`input[name=txtCredit]`).val();
        if(txtCredit==='')
        {
            alert(`学分不能为空`);
            $(`input[name=txtCredit]`).focus();//光标选中
            return false;
        }
        let txtHours=$(`input[name=txtHours]`).val();
        if(txtHours==='')
        {
            alert(`学时不能为空`);
            $(`input[name=txtHours]`).focus();//光标选中
            return false;
        }
        let txtTeacher=$(`input[name=txtTeacher]`).val();
        if(txtTeacher==='')
        {
            alert(`任课老师不能为空`);
            $(`input[name=txtTeacher]`).focus();//光标选中
            return false;
        }
        let txtDate=$(`input[name=txtDate]`).val();
        if(txtDate==='')
        {
            alert(`日期不能为空`);
            $(`input[name=txtDate]`).focus();//光标选中
            return false;
        }
    }
</script>
</body>
</html>
