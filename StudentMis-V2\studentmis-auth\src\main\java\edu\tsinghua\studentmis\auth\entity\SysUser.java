package edu.tsinghua.studentmis.auth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import edu.tsinghua.studentmis.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 系统用户实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
@Schema(description = "系统用户")
public class SysUser extends BaseEntity {

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    @TableField("username")
    private String username;

    @Schema(description = "密码哈希值")
    @JsonIgnore
    @TableField("password_hash")
    private String passwordHash;

    @Schema(description = "密码盐值")
    @JsonIgnore
    @TableField("salt")
    private String salt;

    @Schema(description = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    @TableField("real_name")
    private String realName;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @TableField("email")
    private String email;

    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @TableField("phone")
    private String phone;

    @Schema(description = "头像URL")
    @TableField("avatar_url")
    private String avatarUrl;

    @Schema(description = "用户状态")
    @TableField("status")
    private UserStatus status;

    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    @TableField("last_login_ip")
    private String lastLoginIp;

    @Schema(description = "登录失败次数")
    @TableField("login_fail_count")
    private Integer loginFailCount;

    @Schema(description = "密码过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("password_expire_time")
    private LocalDateTime passwordExpireTime;

    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        LOCKED("锁定"),
        DELETED("已删除");

        private final String description;

        UserStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查用户是否可用
     */
    public boolean isEnabled() {
        return UserStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查用户是否被锁定
     */
    public boolean isLocked() {
        return UserStatus.LOCKED.equals(this.status);
    }

    /**
     * 检查密码是否过期
     */
    public boolean isPasswordExpired() {
        return passwordExpireTime != null && passwordExpireTime.isBefore(LocalDateTime.now());
    }

    /**
     * 重置登录失败次数
     */
    public void resetLoginFailCount() {
        this.loginFailCount = 0;
    }

    /**
     * 增加登录失败次数
     */
    public void incrementLoginFailCount() {
        this.loginFailCount = (this.loginFailCount == null ? 0 : this.loginFailCount) + 1;
    }

    /**
     * 更新最后登录信息
     */
    public void updateLastLoginInfo(String ip) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = ip;
        this.resetLoginFailCount();
    }
}
