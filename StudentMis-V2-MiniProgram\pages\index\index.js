// pages/index/index.js
import { request } from '../../utils/request'
import { formatTime, formatDate } from '../../utils/util'
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    userInfo: {},
    weather: null,
    notices: [],
    currentNotice: {},
    noticeIndex: 0,
    quickActions: [
      { id: 1, name: '成绩查询', icon: 'chart-trending-o', action: 'grades' },
      { id: 2, name: '课程表', icon: 'calendar-o', action: 'schedule' },
      { id: 3, name: '选课', icon: 'add-o', action: 'course-selection' },
      { id: 4, name: '考试安排', icon: 'clock-o', action: 'exams' },
      { id: 5, name: '图书馆', icon: 'book-o', action: 'library' },
      { id: 6, name: '缴费', icon: 'gold-coin-o', action: 'payment' },
      { id: 7, name: '请假', icon: 'edit', action: 'leave' },
      { id: 8, name: '更多', icon: 'ellipsis', action: 'more' }
    ],
    todayCourses: [],
    recentGrades: [],
    studyStats: {
      totalCourses: 0,
      avgScore: 0,
      gpa: 0,
      rank: 0
    },
    recommendations: [],
    loading: true
  },

  onLoad(options) {
    console.log('首页加载', options)
    this.initPage()
  },

  onShow() {
    console.log('首页显示')
    this.refreshData()
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    console.log('触底加载')
    // 可以在这里加载更多推荐内容
  },

  // 初始化页面
  async initPage() {
    try {
      // 检查登录状态
      if (!getApp().isLoggedIn()) {
        wx.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      // 获取用户信息
      this.setData({
        userInfo: getApp().getUserInfo() || {}
      })

      // 加载页面数据
      await this.loadPageData()
    } catch (error) {
      console.error('页面初始化失败:', error)
      Toast.fail('页面加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadPageData()
      Toast.success('刷新成功')
    } catch (error) {
      console.error('刷新失败:', error)
      Toast.fail('刷新失败')
    }
  },

  // 加载页面数据
  async loadPageData() {
    const promises = [
      this.loadWeatherInfo(),
      this.loadNotices(),
      this.loadTodayCourses(),
      this.loadRecentGrades(),
      this.loadStudyStats(),
      this.loadRecommendations()
    ]

    await Promise.allSettled(promises)
  },

  // 加载天气信息
  async loadWeatherInfo() {
    try {
      const res = await request({
        url: '/common/weather',
        method: 'GET'
      })
      
      this.setData({
        weather: res.data
      })
    } catch (error) {
      console.error('获取天气信息失败:', error)
    }
  },

  // 加载通知公告
  async loadNotices() {
    try {
      const res = await request({
        url: '/notifications/latest',
        method: 'GET',
        data: { limit: 5 }
      })
      
      const notices = res.data || []
      this.setData({
        notices,
        currentNotice: notices[0] || {}
      })

      // 自动轮播通知
      if (notices.length > 1) {
        this.startNoticeRotation()
      }
    } catch (error) {
      console.error('获取通知失败:', error)
    }
  },

  // 加载今日课程
  async loadTodayCourses() {
    try {
      const today = formatDate(new Date(), 'YYYY-MM-DD')
      const res = await request({
        url: '/courses/schedule/daily',
        method: 'GET',
        data: { date: today }
      })
      
      const courses = (res.data || []).map(course => {
        const now = new Date()
        const courseStart = new Date(`${today} ${course.startTime}`)
        const courseEnd = new Date(`${today} ${course.endTime}`)
        
        let status = 'upcoming'
        let statusText = '即将开始'
        
        if (now >= courseStart && now <= courseEnd) {
          status = 'ongoing'
          statusText = '进行中'
        } else if (now > courseEnd) {
          status = 'finished'
          statusText = '已结束'
        }
        
        return {
          ...course,
          status,
          statusText
        }
      })
      
      this.setData({
        todayCourses: courses
      })
    } catch (error) {
      console.error('获取今日课程失败:', error)
    }
  },

  // 加载最近成绩
  async loadRecentGrades() {
    try {
      const res = await request({
        url: '/grades/recent',
        method: 'GET',
        data: { limit: 5 }
      })
      
      const grades = (res.data || []).map(grade => {
        let scoreLevel = 'normal'
        if (grade.totalScore >= 90) {
          scoreLevel = 'excellent'
        } else if (grade.totalScore >= 80) {
          scoreLevel = 'good'
        } else if (grade.totalScore < 60) {
          scoreLevel = 'poor'
        }
        
        return {
          ...grade,
          scoreLevel
        }
      })
      
      this.setData({
        recentGrades: grades
      })
    } catch (error) {
      console.error('获取最近成绩失败:', error)
    }
  },

  // 加载学习统计
  async loadStudyStats() {
    try {
      const res = await request({
        url: '/analytics/study-stats',
        method: 'GET'
      })
      
      this.setData({
        studyStats: res.data || {}
      })
    } catch (error) {
      console.error('获取学习统计失败:', error)
    }
  },

  // 加载推荐内容
  async loadRecommendations() {
    try {
      const res = await request({
        url: '/recommendations/personal',
        method: 'GET',
        data: { limit: 3 }
      })
      
      const recommendations = (res.data || []).map(item => {
        let icon = 'star-o'
        switch (item.type) {
          case 'COURSE':
            icon = 'book-o'
            break
          case 'STUDY_METHOD':
            icon = 'bulb-o'
            break
          case 'RESOURCE':
            icon = 'folder-o'
            break
          case 'ACTIVITY':
            icon = 'flag-o'
            break
        }
        
        return {
          ...item,
          icon
        }
      })
      
      this.setData({
        recommendations
      })
    } catch (error) {
      console.error('获取推荐内容失败:', error)
    }
  },

  // 开始通知轮播
  startNoticeRotation() {
    if (this.noticeTimer) {
      clearInterval(this.noticeTimer)
    }
    
    this.noticeTimer = setInterval(() => {
      const { notices, noticeIndex } = this.data
      const nextIndex = (noticeIndex + 1) % notices.length
      
      this.setData({
        noticeIndex: nextIndex,
        currentNotice: notices[nextIndex]
      })
    }, 5000)
  },

  // 快捷功能点击
  onQuickAction(e) {
    const action = e.currentTarget.dataset.action
    
    switch (action) {
      case 'grades':
        wx.switchTab({ url: '/pages/grades/grades' })
        break
      case 'schedule':
        wx.switchTab({ url: '/pages/schedule/schedule' })
        break
      case 'course-selection':
        wx.navigateTo({ url: '/pages/course-selection/index' })
        break
      case 'exams':
        wx.navigateTo({ url: '/pages/exams/index' })
        break
      case 'library':
        wx.navigateTo({ url: '/pages/library/index' })
        break
      case 'payment':
        wx.navigateTo({ url: '/pages/payment/index' })
        break
      case 'leave':
        wx.navigateTo({ url: '/pages/leave/index' })
        break
      case 'more':
        wx.navigateTo({ url: '/pages/more/index' })
        break
      default:
        Toast('功能开发中...')
    }
  },

  // 通知点击
  onNoticeClick() {
    const { currentNotice } = this.data
    if (currentNotice.id) {
      wx.navigateTo({
        url: `/pages/notice-detail/index?id=${currentNotice.id}`
      })
    }
  },

  // 通知关闭
  onNoticeClose() {
    // 可以在这里记录用户关闭的通知，避免重复显示
    console.log('通知已关闭')
  },

  // 课程点击
  onCourseClick(e) {
    const course = e.currentTarget.dataset.course
    wx.navigateTo({
      url: `/pages/course-detail/index?id=${course.id}`
    })
  },

  // 成绩点击
  onGradeClick(e) {
    const grade = e.currentTarget.dataset.grade
    wx.navigateTo({
      url: `/pages/grade-detail/index?id=${grade.id}`
    })
  },

  // 推荐内容点击
  onRecommendationClick(e) {
    const recommendation = e.currentTarget.dataset.recommendation
    
    // 记录点击事件
    this.recordRecommendationClick(recommendation.id)
    
    // 根据推荐类型跳转
    switch (recommendation.type) {
      case 'COURSE':
        wx.navigateTo({
          url: `/pages/course-detail/index?id=${recommendation.itemId}`
        })
        break
      case 'STUDY_METHOD':
        wx.navigateTo({
          url: `/pages/study-method/index?id=${recommendation.itemId}`
        })
        break
      default:
        Toast('功能开发中...')
    }
  },

  // 跳转到课程表
  goToSchedule() {
    wx.switchTab({ url: '/pages/schedule/schedule' })
  },

  // 跳转到成绩页面
  goToGrades() {
    wx.switchTab({ url: '/pages/grades/grades' })
  },

  // 记录推荐点击
  async recordRecommendationClick(recommendationId) {
    try {
      await request({
        url: '/recommendations/click',
        method: 'POST',
        data: { recommendationId }
      })
    } catch (error) {
      console.error('记录推荐点击失败:', error)
    }
  },

  onUnload() {
    // 清理定时器
    if (this.noticeTimer) {
      clearInterval(this.noticeTimer)
    }
  }
})
