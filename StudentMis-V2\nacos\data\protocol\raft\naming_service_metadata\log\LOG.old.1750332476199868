2025/06/19-19:26:11.771890 46bc RocksDB version: 7.7.3
2025/06/19-19:26:11.771983 46bc Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-19:26:11.772002 46bc Compile date 2022-10-24 17:17:55
2025/06/19-19:26:11.772016 46bc DB SUMMARY
2025/06/19-19:26:11.772029 46bc DB Session ID:  K4XFAHQXE6EOWWNXS3ES
2025/06/19-19:26:11.772453 46bc SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log dir, Total Num: 0, files: 
2025/06/19-19:26:11.772474 46bc Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log: 
2025/06/19-19:26:11.772488 46bc                         Options.error_if_exists: 0
2025/06/19-19:26:11.772709 46bc                       Options.create_if_missing: 1
2025/06/19-19:26:11.772724 46bc                         Options.paranoid_checks: 1
2025/06/19-19:26:11.772727 46bc             Options.flush_verify_memtable_count: 1
2025/06/19-19:26:11.772730 46bc                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-19:26:11.772733 46bc        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-19:26:11.772736 46bc                                     Options.env: 000001366FB452F0
2025/06/19-19:26:11.772740 46bc                                      Options.fs: WinFS
2025/06/19-19:26:11.772743 46bc                                Options.info_log: 000001366F7330B0
2025/06/19-19:26:11.772746 46bc                Options.max_file_opening_threads: 16
2025/06/19-19:26:11.772749 46bc                              Options.statistics: 000001366FD92460
2025/06/19-19:26:11.772752 46bc                               Options.use_fsync: 0
2025/06/19-19:26:11.772755 46bc                       Options.max_log_file_size: 0
2025/06/19-19:26:11.772758 46bc                  Options.max_manifest_file_size: 1073741824
2025/06/19-19:26:11.772761 46bc                   Options.log_file_time_to_roll: 0
2025/06/19-19:26:11.772764 46bc                       Options.keep_log_file_num: 100
2025/06/19-19:26:11.772767 46bc                    Options.recycle_log_file_num: 0
2025/06/19-19:26:11.772769 46bc                         Options.allow_fallocate: 1
2025/06/19-19:26:11.772772 46bc                        Options.allow_mmap_reads: 0
2025/06/19-19:26:11.772775 46bc                       Options.allow_mmap_writes: 0
2025/06/19-19:26:11.772778 46bc                        Options.use_direct_reads: 0
2025/06/19-19:26:11.772781 46bc                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-19:26:11.772783 46bc          Options.create_missing_column_families: 1
2025/06/19-19:26:11.772786 46bc                              Options.db_log_dir: 
2025/06/19-19:26:11.772789 46bc                                 Options.wal_dir: 
2025/06/19-19:26:11.772792 46bc                Options.table_cache_numshardbits: 6
2025/06/19-19:26:11.772795 46bc                         Options.WAL_ttl_seconds: 0
2025/06/19-19:26:11.772797 46bc                       Options.WAL_size_limit_MB: 0
2025/06/19-19:26:11.772800 46bc                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-19:26:11.772803 46bc             Options.manifest_preallocation_size: 4194304
2025/06/19-19:26:11.772806 46bc                     Options.is_fd_close_on_exec: 1
2025/06/19-19:26:11.772809 46bc                   Options.advise_random_on_open: 1
2025/06/19-19:26:11.772812 46bc                    Options.db_write_buffer_size: 0
2025/06/19-19:26:11.772814 46bc                    Options.write_buffer_manager: 000001366FB42050
2025/06/19-19:26:11.772817 46bc         Options.access_hint_on_compaction_start: 1
2025/06/19-19:26:11.772820 46bc           Options.random_access_max_buffer_size: 1048576
2025/06/19-19:26:11.772823 46bc                      Options.use_adaptive_mutex: 0
2025/06/19-19:26:11.772826 46bc                            Options.rate_limiter: 0000000000000000
2025/06/19-19:26:11.772829 46bc     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-19:26:11.772832 46bc                       Options.wal_recovery_mode: 2
2025/06/19-19:26:11.772864 46bc                  Options.enable_thread_tracking: 0
2025/06/19-19:26:11.772869 46bc                  Options.enable_pipelined_write: 0
2025/06/19-19:26:11.772872 46bc                  Options.unordered_write: 0
2025/06/19-19:26:11.772874 46bc         Options.allow_concurrent_memtable_write: 1
2025/06/19-19:26:11.772877 46bc      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-19:26:11.772880 46bc             Options.write_thread_max_yield_usec: 100
2025/06/19-19:26:11.772883 46bc            Options.write_thread_slow_yield_usec: 3
2025/06/19-19:26:11.772886 46bc                               Options.row_cache: None
2025/06/19-19:26:11.772888 46bc                              Options.wal_filter: None
2025/06/19-19:26:11.772891 46bc             Options.avoid_flush_during_recovery: 0
2025/06/19-19:26:11.772894 46bc             Options.allow_ingest_behind: 0
2025/06/19-19:26:11.772897 46bc             Options.two_write_queues: 0
2025/06/19-19:26:11.772900 46bc             Options.manual_wal_flush: 0
2025/06/19-19:26:11.772903 46bc             Options.wal_compression: 0
2025/06/19-19:26:11.772905 46bc             Options.atomic_flush: 0
2025/06/19-19:26:11.772908 46bc             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-19:26:11.772911 46bc                 Options.persist_stats_to_disk: 0
2025/06/19-19:26:11.772914 46bc                 Options.write_dbid_to_manifest: 0
2025/06/19-19:26:11.772916 46bc                 Options.log_readahead_size: 0
2025/06/19-19:26:11.772919 46bc                 Options.file_checksum_gen_factory: Unknown
2025/06/19-19:26:11.772922 46bc                 Options.best_efforts_recovery: 0
2025/06/19-19:26:11.772925 46bc                Options.max_bgerror_resume_count: 2147483647
2025/06/19-19:26:11.772928 46bc            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-19:26:11.772931 46bc             Options.allow_data_in_errors: 0
2025/06/19-19:26:11.772934 46bc             Options.db_host_id: __hostname__
2025/06/19-19:26:11.772936 46bc             Options.enforce_single_del_contracts: true
2025/06/19-19:26:11.772939 46bc             Options.max_background_jobs: 2
2025/06/19-19:26:11.772942 46bc             Options.max_background_compactions: 4
2025/06/19-19:26:11.772945 46bc             Options.max_subcompactions: 1
2025/06/19-19:26:11.772948 46bc             Options.avoid_flush_during_shutdown: 0
2025/06/19-19:26:11.772951 46bc           Options.writable_file_max_buffer_size: 1048576
2025/06/19-19:26:11.772954 46bc             Options.delayed_write_rate : 16777216
2025/06/19-19:26:11.772957 46bc             Options.max_total_wal_size: 1073741824
2025/06/19-19:26:11.772960 46bc             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-19:26:11.772963 46bc                   Options.stats_dump_period_sec: 600
2025/06/19-19:26:11.772967 46bc                 Options.stats_persist_period_sec: 600
2025/06/19-19:26:11.772970 46bc                 Options.stats_history_buffer_size: 1048576
2025/06/19-19:26:11.772973 46bc                          Options.max_open_files: -1
2025/06/19-19:26:11.772976 46bc                          Options.bytes_per_sync: 0
2025/06/19-19:26:11.772979 46bc                      Options.wal_bytes_per_sync: 0
2025/06/19-19:26:11.772983 46bc                   Options.strict_bytes_per_sync: 0
2025/06/19-19:26:11.772987 46bc       Options.compaction_readahead_size: 0
2025/06/19-19:26:11.773006 46bc                  Options.max_background_flushes: 1
2025/06/19-19:26:11.773010 46bc Compression algorithms supported:
2025/06/19-19:26:11.773014 46bc 	kZSTD supported: 1
2025/06/19-19:26:11.773017 46bc 	kSnappyCompression supported: 1
2025/06/19-19:26:11.773020 46bc 	kBZip2Compression supported: 0
2025/06/19-19:26:11.773023 46bc 	kZlibCompression supported: 1
2025/06/19-19:26:11.773027 46bc 	kLZ4Compression supported: 1
2025/06/19-19:26:11.773029 46bc 	kXpressCompression supported: 0
2025/06/19-19:26:11.773032 46bc 	kLZ4HCCompression supported: 1
2025/06/19-19:26:11.773063 46bc 	kZSTDNotFinalCompression supported: 1
2025/06/19-19:26:11.773069 46bc Fast CRC32 supported: Not supported on x86
2025/06/19-19:26:11.773072 46bc DMutex implementation: std::mutex
2025/06/19-19:26:11.775825 46bc [db\db_impl\db_impl_open.cc:313] Creating manifest 1 
2025/06/19-19:26:11.778881 46bc [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000001
2025/06/19-19:26:11.779079 46bc [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-19:26:11.779087 46bc               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:26:11.779090 46bc           Options.merge_operator: StringAppendOperator
2025/06/19-19:26:11.779093 46bc        Options.compaction_filter: None
2025/06/19-19:26:11.779096 46bc        Options.compaction_filter_factory: None
2025/06/19-19:26:11.779099 46bc  Options.sst_partitioner_factory: None
2025/06/19-19:26:11.779101 46bc         Options.memtable_factory: SkipListFactory
2025/06/19-19:26:11.779104 46bc            Options.table_factory: BlockBasedTable
2025/06/19-19:26:11.779138 46bc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001366F26C8D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000013670653670
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:26:11.779141 46bc        Options.write_buffer_size: 67108864
2025/06/19-19:26:11.779144 46bc  Options.max_write_buffer_number: 3
2025/06/19-19:26:11.779147 46bc          Options.compression: Snappy
2025/06/19-19:26:11.779150 46bc                  Options.bottommost_compression: Disabled
2025/06/19-19:26:11.779153 46bc       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:26:11.779156 46bc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:26:11.779159 46bc             Options.num_levels: 7
2025/06/19-19:26:11.779161 46bc        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:26:11.779166 46bc     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:26:11.779169 46bc     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:26:11.779172 46bc            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:26:11.779174 46bc                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:26:11.779177 46bc               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:26:11.779180 46bc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.779183 46bc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.779185 46bc         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:26:11.779188 46bc                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:26:11.779191 46bc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.779194 46bc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.779200 46bc            Options.compression_opts.window_bits: -14
2025/06/19-19:26:11.779204 46bc                  Options.compression_opts.level: 32767
2025/06/19-19:26:11.779207 46bc               Options.compression_opts.strategy: 0
2025/06/19-19:26:11.779209 46bc         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.779212 46bc         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.779215 46bc         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.779218 46bc         Options.compression_opts.parallel_threads: 1
2025/06/19-19:26:11.779220 46bc                  Options.compression_opts.enabled: false
2025/06/19-19:26:11.779223 46bc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.779226 46bc      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:26:11.779228 46bc          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:26:11.779231 46bc              Options.level0_stop_writes_trigger: 40
2025/06/19-19:26:11.779234 46bc                   Options.target_file_size_base: 67108864
2025/06/19-19:26:11.779236 46bc             Options.target_file_size_multiplier: 1
2025/06/19-19:26:11.779239 46bc                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:26:11.779242 46bc Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:26:11.779244 46bc          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:26:11.779248 46bc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:26:11.779251 46bc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:26:11.779253 46bc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:26:11.779256 46bc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:26:11.779259 46bc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:26:11.779261 46bc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:26:11.779264 46bc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:26:11.779267 46bc       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:26:11.779269 46bc                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:26:11.779272 46bc                        Options.arena_block_size: 1048576
2025/06/19-19:26:11.779275 46bc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:26:11.779278 46bc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:26:11.779281 46bc                Options.disable_auto_compactions: 0
2025/06/19-19:26:11.779284 46bc                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:26:11.779287 46bc                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:26:11.779290 46bc Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:26:11.779293 46bc Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:26:11.779295 46bc Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:26:11.779298 46bc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:26:11.779301 46bc Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:26:11.779304 46bc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:26:11.779307 46bc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:26:11.779310 46bc Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:26:11.779314 46bc                   Options.table_properties_collectors: 
2025/06/19-19:26:11.779317 46bc                   Options.inplace_update_support: 0
2025/06/19-19:26:11.779319 46bc                 Options.inplace_update_num_locks: 10000
2025/06/19-19:26:11.779322 46bc               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:26:11.779325 46bc               Options.memtable_whole_key_filtering: 0
2025/06/19-19:26:11.779328 46bc   Options.memtable_huge_page_size: 0
2025/06/19-19:26:11.779364 46bc                           Options.bloom_locality: 0
2025/06/19-19:26:11.779368 46bc                    Options.max_successive_merges: 0
2025/06/19-19:26:11.779371 46bc                Options.optimize_filters_for_hits: 0
2025/06/19-19:26:11.779374 46bc                Options.paranoid_file_checks: 0
2025/06/19-19:26:11.779376 46bc                Options.force_consistency_checks: 1
2025/06/19-19:26:11.779379 46bc                Options.report_bg_io_stats: 0
2025/06/19-19:26:11.779382 46bc                               Options.ttl: 2592000
2025/06/19-19:26:11.779385 46bc          Options.periodic_compaction_seconds: 0
2025/06/19-19:26:11.779387 46bc  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:26:11.779390 46bc                       Options.enable_blob_files: false
2025/06/19-19:26:11.779392 46bc                           Options.min_blob_size: 0
2025/06/19-19:26:11.779395 46bc                          Options.blob_file_size: 268435456
2025/06/19-19:26:11.779398 46bc                   Options.blob_compression_type: NoCompression
2025/06/19-19:26:11.779401 46bc          Options.enable_blob_garbage_collection: false
2025/06/19-19:26:11.779404 46bc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:26:11.779407 46bc Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:26:11.779410 46bc          Options.blob_compaction_readahead_size: 0
2025/06/19-19:26:11.779412 46bc                Options.blob_file_starting_level: 0
2025/06/19-19:26:11.779415 46bc Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:26:11.782401 46bc [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/06/19-19:26:11.782416 46bc [db\version_set.cc:5588] Column family [default] (ID 0), log number is 0
2025/06/19-19:26:11.782877 46bc [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9f-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-19:26:11.784281 46bc [db\version_set.cc:5051] Creating manifest 5
2025/06/19-19:26:11.787578 46bc [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-19:26:11.787588 46bc               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:26:11.787592 46bc           Options.merge_operator: StringAppendOperator
2025/06/19-19:26:11.787594 46bc        Options.compaction_filter: None
2025/06/19-19:26:11.787597 46bc        Options.compaction_filter_factory: None
2025/06/19-19:26:11.787600 46bc  Options.sst_partitioner_factory: None
2025/06/19-19:26:11.787602 46bc         Options.memtable_factory: SkipListFactory
2025/06/19-19:26:11.787605 46bc            Options.table_factory: BlockBasedTable
2025/06/19-19:26:11.787636 46bc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001366F26C8D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000013670653670
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:26:11.787644 46bc        Options.write_buffer_size: 67108864
2025/06/19-19:26:11.787648 46bc  Options.max_write_buffer_number: 3
2025/06/19-19:26:11.787651 46bc          Options.compression: Snappy
2025/06/19-19:26:11.787653 46bc                  Options.bottommost_compression: Disabled
2025/06/19-19:26:11.787657 46bc       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:26:11.787659 46bc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:26:11.787662 46bc             Options.num_levels: 7
2025/06/19-19:26:11.787664 46bc        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:26:11.787667 46bc     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:26:11.787670 46bc     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:26:11.787672 46bc            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:26:11.787675 46bc                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:26:11.787678 46bc               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:26:11.787680 46bc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.787683 46bc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.787686 46bc         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:26:11.787688 46bc                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:26:11.787691 46bc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.787694 46bc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.787697 46bc            Options.compression_opts.window_bits: -14
2025/06/19-19:26:11.787699 46bc                  Options.compression_opts.level: 32767
2025/06/19-19:26:11.787702 46bc               Options.compression_opts.strategy: 0
2025/06/19-19:26:11.787704 46bc         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.787707 46bc         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.787710 46bc         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.787712 46bc         Options.compression_opts.parallel_threads: 1
2025/06/19-19:26:11.787715 46bc                  Options.compression_opts.enabled: false
2025/06/19-19:26:11.787717 46bc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.787720 46bc      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:26:11.787722 46bc          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:26:11.787725 46bc              Options.level0_stop_writes_trigger: 40
2025/06/19-19:26:11.787728 46bc                   Options.target_file_size_base: 67108864
2025/06/19-19:26:11.787730 46bc             Options.target_file_size_multiplier: 1
2025/06/19-19:26:11.787733 46bc                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:26:11.787735 46bc Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:26:11.787738 46bc          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:26:11.787741 46bc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:26:11.787744 46bc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:26:11.787747 46bc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:26:11.787749 46bc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:26:11.787752 46bc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:26:11.787754 46bc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:26:11.787757 46bc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:26:11.787760 46bc       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:26:11.787762 46bc                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:26:11.787765 46bc                        Options.arena_block_size: 1048576
2025/06/19-19:26:11.787769 46bc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:26:11.787772 46bc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:26:11.787775 46bc                Options.disable_auto_compactions: 0
2025/06/19-19:26:11.787778 46bc                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:26:11.787781 46bc                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:26:11.787784 46bc Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:26:11.787787 46bc Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:26:11.787789 46bc Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:26:11.787792 46bc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:26:11.787795 46bc Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:26:11.787798 46bc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:26:11.787801 46bc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:26:11.787804 46bc Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:26:11.787808 46bc                   Options.table_properties_collectors: 
2025/06/19-19:26:11.787811 46bc                   Options.inplace_update_support: 0
2025/06/19-19:26:11.787814 46bc                 Options.inplace_update_num_locks: 10000
2025/06/19-19:26:11.787816 46bc               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:26:11.787819 46bc               Options.memtable_whole_key_filtering: 0
2025/06/19-19:26:11.787822 46bc   Options.memtable_huge_page_size: 0
2025/06/19-19:26:11.787825 46bc                           Options.bloom_locality: 0
2025/06/19-19:26:11.787827 46bc                    Options.max_successive_merges: 0
2025/06/19-19:26:11.787830 46bc                Options.optimize_filters_for_hits: 0
2025/06/19-19:26:11.787832 46bc                Options.paranoid_file_checks: 0
2025/06/19-19:26:11.787835 46bc                Options.force_consistency_checks: 1
2025/06/19-19:26:11.787838 46bc                Options.report_bg_io_stats: 0
2025/06/19-19:26:11.787840 46bc                               Options.ttl: 2592000
2025/06/19-19:26:11.787843 46bc          Options.periodic_compaction_seconds: 0
2025/06/19-19:26:11.787845 46bc  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:26:11.787848 46bc                       Options.enable_blob_files: false
2025/06/19-19:26:11.787851 46bc                           Options.min_blob_size: 0
2025/06/19-19:26:11.787853 46bc                          Options.blob_file_size: 268435456
2025/06/19-19:26:11.787856 46bc                   Options.blob_compression_type: NoCompression
2025/06/19-19:26:11.787859 46bc          Options.enable_blob_garbage_collection: false
2025/06/19-19:26:11.787861 46bc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:26:11.787864 46bc Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:26:11.787867 46bc          Options.blob_compaction_readahead_size: 0
2025/06/19-19:26:11.787870 46bc                Options.blob_file_starting_level: 0
2025/06/19-19:26:11.787872 46bc Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:26:11.789956 46bc [db\db_impl\db_impl.cc:3086] Created column family [Configuration] (ID 1)
2025/06/19-19:26:11.797399 46bc [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000001366D4945D0
2025/06/19-19:26:11.797852 46bc DB pointer 000001366D32BDC0
2025/06/19-19:26:17.799474 2b64 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-19:26:17.799493 2b64 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 6.0 total, 6.0 interval
Cumulative writes: 1 writes, 2 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 1 writes, 1 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 2 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 1 writes, 1 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.0 total, 6.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000013670653670#5236 capacity: 512.00 MB usage: 0.08 KB table_size: 4096 occupancy: 1 collections: 1 last_copies: 1 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.0 total, 6.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000013670653670#5236 capacity: 512.00 MB usage: 0.08 KB table_size: 4096 occupancy: 1 collections: 1 last_copies: 1 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [Configuration] **
2025/06/19-19:26:17.800110 2b64 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 0
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 0
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 0
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 0
rocksdb.block.cache.index.bytes.insert COUNT : 0
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 0
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 0
rocksdb.block.cache.data.bytes.insert COUNT : 0
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 0
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 1
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 2
rocksdb.number.keys.read COUNT : 1
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 103
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 3
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 0
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 103
rocksdb.write.self COUNT : 1
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 1
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 0
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 3
rocksdb.num.iterator.deleted COUNT : 3
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 0
rocksdb.non.last.level.read.count COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 4.000000 P95 : 4.000000 P99 : 4.000000 P100 : 4.000000 COUNT : 1 SUM : 4
rocksdb.db.write.micros P50 : 554.000000 P95 : 554.000000 P99 : 554.000000 P100 : 554.000000 COUNT : 1 SUM : 554
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 379.000000 P95 : 379.000000 P99 : 379.000000 P100 : 379.000000 COUNT : 1 SUM : 379
rocksdb.manifest.file.sync.micros P50 : 430.000000 P95 : 480.000000 P99 : 480.000000 P100 : 480.000000 COUNT : 3 SUM : 1142
rocksdb.table.open.io.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.write.raw.block.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 1 SUM : 0
rocksdb.bytes.per.write P50 : 103.000000 P95 : 103.000000 P99 : 103.000000 P100 : 103.000000 COUNT : 1 SUM : 103
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-19:26:18.799397 2b64 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-19:26:18.799430 2b64 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-19:26:18.799434 2b64 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
