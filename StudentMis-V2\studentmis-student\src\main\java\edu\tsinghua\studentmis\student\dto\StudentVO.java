package edu.tsinghua.studentmis.student.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import edu.tsinghua.studentmis.student.entity.Student;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学生信息VO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "学生信息视图对象")
public class StudentVO {

    @Schema(description = "学生ID")
    private Long id;

    @Schema(description = "学号")
    private String studentId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "英文姓名")
    private String nameEn;

    @Schema(description = "性别")
    private Student.Gender gender;

    @Schema(description = "性别描述")
    private String genderDesc;

    @Schema(description = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "国籍")
    private String nationality;

    @Schema(description = "民族")
    private String ethnicity;

    @Schema(description = "政治面貌")
    private String politicalStatus;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "紧急联系人")
    private String emergencyContact;

    @Schema(description = "紧急联系电话")
    private String emergencyPhone;

    @Schema(description = "入学日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate admissionDate;

    @Schema(description = "毕业日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate graduationDate;

    @Schema(description = "在校年限")
    private Integer schoolYears;

    @Schema(description = "学籍状态")
    private Student.StudentStatus status;

    @Schema(description = "学籍状态描述")
    private String statusDesc;

    @Schema(description = "专业ID")
    private Long majorId;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "班级ID")
    private Long classId;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "院系名称")
    private String departmentName;

    @Schema(description = "宿舍")
    private String dormitory;

    @Schema(description = "照片URL")
    private String photoUrl;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 设置性别描述
    public void setGender(Student.Gender gender) {
        this.gender = gender;
        this.genderDesc = gender != null ? gender.getDescription() : null;
    }

    // 设置状态描述
    public void setStatus(Student.StudentStatus status) {
        this.status = status;
        this.statusDesc = status != null ? status.getDescription() : null;
    }
}
