import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { login, logout, getUserInfo, refreshToken } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router from '@/router'
import type { LoginForm, UserInfo } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const userInfo = ref<UserInfo | null>(null)
  const roles = ref<string[]>([])
  const permissions = ref<string[]>([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.realName || userInfo.value?.username || '')
  const avatar = computed(() => userInfo.value?.avatarUrl || '')

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      const { token: newToken, ...userInfoData } = response.data
      
      // 保存token
      token.value = newToken
      setToken(newToken)
      
      // 保存用户信息
      userInfo.value = userInfoData
      roles.value = userInfoData.roles || []
      permissions.value = userInfoData.permissions || []
      
      ElMessage.success('登录成功')
      
      // 跳转到首页
      await router.push('/')
      
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      const userInfoData = response.data
      
      userInfo.value = userInfoData
      roles.value = userInfoData.roles || []
      permissions.value = userInfoData.permissions || []
      
      return userInfoData
    } catch (error: any) {
      ElMessage.error('获取用户信息失败')
      throw error
    }
  }

  // 刷新token
  const refreshTokenAction = async () => {
    try {
      const response = await refreshToken()
      const { token: newToken, ...userInfoData } = response.data
      
      token.value = newToken
      setToken(newToken)
      
      userInfo.value = userInfoData
      roles.value = userInfoData.roles || []
      permissions.value = userInfoData.permissions || []
      
      return newToken
    } catch (error: any) {
      console.error('刷新token失败:', error)
      logoutAction()
      throw error
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      if (token.value) {
        await logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = null
      roles.value = []
      permissions.value = []
      removeToken()
      
      // 跳转到登录页
      await router.push('/login')
      
      ElMessage.success('已退出登录')
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  // 检查多个权限（任一满足）
  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  // 检查多个角色（任一满足）
  const hasAnyRole = (roleList: string[]): boolean => {
    return roleList.some(role => hasRole(role))
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...newUserInfo }
    }
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    roles.value = []
    permissions.value = []
    removeToken()
  }

  return {
    // 状态
    token,
    userInfo,
    roles,
    permissions,
    
    // 计算属性
    isLoggedIn,
    userName,
    avatar,
    
    // 方法
    login: loginAction,
    logout: logoutAction,
    getUserInfo: getUserInfoAction,
    refreshToken: refreshTokenAction,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole,
    updateUserInfo,
    resetState
  }
})
