package edu.tsinghua.studentmis.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.SecretKey;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全工具类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Component
@Slf4j
public class SecurityUtils {

    private static String jwtSecret;
    private static Long jwtExpiration;
    private static String tokenHeader;
    private static String tokenPrefix;

    @Value("${jwt.secret:studentmis-v2-secret-key-for-jwt-token-generation}")
    public void setJwtSecret(String jwtSecret) {
        SecurityUtils.jwtSecret = jwtSecret;
    }

    @Value("${jwt.expiration:86400}")
    public void setJwtExpiration(Long jwtExpiration) {
        SecurityUtils.jwtExpiration = jwtExpiration;
    }

    @Value("${jwt.header:Authorization}")
    public void setTokenHeader(String tokenHeader) {
        SecurityUtils.tokenHeader = tokenHeader;
    }

    @Value("${jwt.prefix:Bearer }")
    public void setTokenPrefix(String tokenPrefix) {
        SecurityUtils.tokenPrefix = tokenPrefix;
    }

    /**
     * 生成JWT令牌
     */
    public static String generateToken(Long userId, String username, String realName, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("realName", realName);
        claims.put("role", role);
        
        return createToken(claims, username);
    }

    /**
     * 创建令牌
     */
    private static String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + jwtExpiration * 1000);
        
        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(key)
                .compact();
    }

    /**
     * 从令牌中获取Claims
     */
    public static Claims getClaimsFromToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("解析JWT令牌失败: {}", e.getMessage());
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }
    }

    /**
     * 验证令牌是否过期
     */
    public static boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 从请求中获取令牌
     */
    public static String getTokenFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        
        HttpServletRequest request = attributes.getRequest();
        String authHeader = request.getHeader(tokenHeader);
        
        if (StrUtil.isNotBlank(authHeader) && authHeader.startsWith(tokenPrefix)) {
            return authHeader.substring(tokenPrefix.length());
        }
        
        return null;
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        String token = getTokenFromRequest();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }
        
        Claims claims = getClaimsFromToken(token);
        Object userId = claims.get("userId");
        
        if (userId == null) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }
        
        return Long.valueOf(userId.toString());
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        String token = getTokenFromRequest();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }
        
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }

    /**
     * 获取当前用户真实姓名
     */
    public static String getCurrentRealName() {
        String token = getTokenFromRequest();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }
        
        Claims claims = getClaimsFromToken(token);
        Object realName = claims.get("realName");
        
        return realName != null ? realName.toString() : "";
    }

    /**
     * 获取当前用户角色
     */
    public static String getCurrentRole() {
        String token = getTokenFromRequest();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }
        
        Claims claims = getClaimsFromToken(token);
        Object role = claims.get("role");
        
        return role != null ? role.toString() : "";
    }

    /**
     * 刷新令牌
     */
    public static String refreshToken(String token) {
        Claims claims = getClaimsFromToken(token);
        
        Long userId = Long.valueOf(claims.get("userId").toString());
        String username = claims.getSubject();
        String realName = claims.get("realName").toString();
        String role = claims.get("role").toString();
        
        return generateToken(userId, username, realName, role);
    }
}
