# StudentMIS V2 部署指南

## 概述

本指南将帮助您在任何Windows/macOS/Linux电脑上成功部署和运行StudentMIS V2系统。

## 系统要求

### 硬件要求
- **CPU**: 4核心或以上
- **内存**: 8GB RAM或以上
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **JDK**: 17或更高版本
- **Node.js**: 18或更高版本
- **Maven**: 3.8或更高版本
- **MySQL**: 8.0或更高版本
- **Redis**: 7.0或更高版本

## 快速部署 (推荐)

### Windows用户

1. **以管理员身份运行PowerShell或命令提示符**

2. **自动安装依赖**
   ```cmd
   cd StudentMis-V2\scripts
   install-dependencies.bat
   ```

3. **启动系统**
   ```cmd
   start-all.bat
   ```

4. **测试系统**
   ```cmd
   test-system.bat
   ```

### macOS/Linux用户

1. **打开终端**

2. **给脚本执行权限**
   ```bash
   chmod +x scripts/*.sh
   ```

3. **启动系统**
   ```bash
   ./scripts/start-all.sh
   ```

## 手动部署

### 步骤1: 环境准备

#### 安装JDK 17
```bash
# Windows (使用Chocolatey)
choco install openjdk17

# macOS (使用Homebrew)
brew install openjdk@17

# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-17-jdk

# 验证安装
java -version
```

#### 安装Node.js 18
```bash
# 下载并安装Node.js 18 LTS
# https://nodejs.org/

# 验证安装
node --version
npm --version
```

#### 安装Maven
```bash
# Windows
choco install maven

# macOS
brew install maven

# Ubuntu/Debian
sudo apt install maven

# 验证安装
mvn -version
```

#### 安装MySQL 8.0
```bash
# Windows: 下载MySQL Installer
# https://dev.mysql.com/downloads/installer/

# macOS
brew install mysql

# Ubuntu/Debian
sudo apt install mysql-server-8.0

# 启动MySQL服务
# Windows: net start mysql
# macOS: brew services start mysql
# Linux: sudo systemctl start mysql
```

#### 安装Redis
```bash
# Windows: 下载Redis for Windows
# https://github.com/microsoftarchive/redis/releases

# macOS
brew install redis

# Ubuntu/Debian
sudo apt install redis-server

# 启动Redis服务
# Windows: redis-server
# macOS: brew services start redis
# Linux: sudo systemctl start redis
```

### 步骤2: 数据库配置

#### 创建数据库和用户
```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE studentmis_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'studentmis'@'localhost' IDENTIFIED BY 'StudentMIS@2024';
GRANT ALL PRIVILEGES ON studentmis_v2.* TO 'studentmis'@'localhost';
FLUSH PRIVILEGES;

-- 退出MySQL
exit;
```

#### 导入数据库结构
```bash
# 导入数据库脚本
mysql -u studentmis -p studentmis_v2 < studentmis_v2_database.sql
```

### 步骤3: 下载和配置Nacos

```bash
# 下载Nacos 2.3.0
wget https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.tar.gz

# 解压
tar -xzf nacos-server-2.3.0.tar.gz

# Windows用户下载zip文件并解压到项目根目录
```

### 步骤4: 配置应用

#### 后端配置
```bash
# 复制配置文件模板
cp src/main/resources/application-dev.yml.template src/main/resources/application-dev.yml

# 编辑配置文件，修改数据库连接信息
# 主要修改以下配置：
# - spring.datasource.url
# - spring.datasource.username  
# - spring.datasource.password
# - spring.redis.host
# - spring.redis.port
```

#### 前端配置
```bash
cd StudentMis-V2-Frontend

# 复制环境配置
cp .env.development.template .env.development

# 根据需要修改API地址
```

### 步骤5: 启动服务

#### 启动Nacos
```bash
# Linux/macOS
cd nacos/bin
./startup.sh -m standalone

# Windows
cd nacos\bin
startup.cmd -m standalone
```

#### 编译项目
```bash
cd StudentMis-V2
mvn clean compile
```

#### 启动后端服务
```bash
# 启动网关服务
cd studentmis-gateway
mvn spring-boot:run -Dspring-boot.run.profiles=dev &

# 启动认证服务
cd ../studentmis-auth
mvn spring-boot:run -Dspring-boot.run.profiles=dev &

# 启动学生服务
cd ../studentmis-student
mvn spring-boot:run -Dspring-boot.run.profiles=dev &

# 启动成绩服务
cd ../studentmis-grade
mvn spring-boot:run -Dspring-boot.run.profiles=dev &

# 启动数据分析服务
cd ../studentmis-analytics
mvn spring-boot:run -Dspring-boot.run.profiles=dev &
```

#### 启动前端服务
```bash
cd StudentMis-V2-Frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 步骤6: 验证部署

访问以下地址验证系统是否正常运行：

- **前端应用**: http://localhost:3000
- **API网关**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos
- **API文档**: http://localhost:8080/doc.html

## 生产环境部署

### 构建生产版本

#### 后端构建
```bash
cd StudentMis-V2
mvn clean package -Dmaven.test.skip=true
```

#### 前端构建
```bash
cd StudentMis-V2-Frontend
npm run build
```

### 生产环境配置

#### 数据库优化
```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL max_connections = 1000;
SET GLOBAL query_cache_size = 268435456; -- 256MB
```

#### JVM参数优化
```bash
# 启动参数示例
java -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/logs/ \
     -jar studentmis-gateway-2.0.0.jar
```

#### Nginx反向代理配置
```nginx
upstream studentmis_backend {
    server 127.0.0.1:8080;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://studentmis_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 故障排除

### 常见问题及解决方案

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 杀死占用端口的进程
kill -9 <PID>
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql

# 重启MySQL服务
sudo systemctl restart mysql

# 检查防火墙设置
sudo ufw allow 3306
```

#### 3. 内存不足
```bash
# 检查系统内存使用
free -h

# 调整JVM堆内存大小
export JAVA_OPTS="-Xms1g -Xmx2g"
```

#### 4. Nacos连接失败
```bash
# 检查Nacos日志
tail -f nacos/logs/start.out

# 重启Nacos
cd nacos/bin
./shutdown.sh
./startup.sh -m standalone
```

### 日志查看

#### 应用日志位置
- **网关服务**: `studentmis-gateway/logs/`
- **认证服务**: `studentmis-auth/logs/`
- **学生服务**: `studentmis-student/logs/`
- **成绩服务**: `studentmis-grade/logs/`
- **分析服务**: `studentmis-analytics/logs/`

#### 查看实时日志
```bash
# 查看特定服务日志
tail -f studentmis-gateway/logs/application.log

# 查看错误日志
grep ERROR studentmis-*/logs/application.log
```

## 性能优化

### 数据库优化
1. 创建适当的索引
2. 定期清理日志表
3. 配置连接池参数
4. 启用查询缓存

### 应用优化
1. 调整JVM参数
2. 配置Redis缓存
3. 启用Gzip压缩
4. 使用CDN加速静态资源

### 监控配置
1. 配置Prometheus监控
2. 设置Grafana仪表板
3. 配置告警规则
4. 定期备份数据

## 安全配置

### 网络安全
1. 配置防火墙规则
2. 使用HTTPS证书
3. 限制数据库访问IP
4. 定期更新系统补丁

### 应用安全
1. 修改默认密码
2. 配置JWT密钥
3. 启用访问日志
4. 定期安全扫描

## 备份策略

### 数据库备份
```bash
# 每日备份脚本
mysqldump -u studentmis -p studentmis_v2 > backup_$(date +%Y%m%d).sql

# 自动备份cron任务
0 2 * * * /path/to/backup_script.sh
```

### 应用备份
```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz src/main/resources/

# 备份日志文件
tar -czf logs_backup_$(date +%Y%m%d).tar.gz */logs/
```

## 技术支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 验证网络连接
4. 参考故障排除章节
5. 联系技术支持团队

---

**注意**: 本指南基于标准环境编写，实际部署时可能需要根据具体环境进行调整。
