package edu.tsinghua.studentmis.grade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 成绩查询请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "成绩查询请求")
public class GradeQueryRequest {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学号")
    private String studentNumber;

    @Schema(description = "课程安排ID")
    private Long scheduleId;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "学期")
    private String semester;

    @Schema(description = "学年")
    private String academicYear;

    @Schema(description = "成绩状态")
    private String status;

    @Schema(description = "班级ID")
    private Long classId;

    @Schema(description = "专业ID")
    private Long majorId;

    @Schema(description = "最低分数")
    private Double minScore;

    @Schema(description = "最高分数")
    private Double maxScore;

    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "页大小", defaultValue = "10")
    private Integer pageSize = 10;

    @Schema(description = "排序字段")
    private String orderBy;

    @Schema(description = "排序方向", allowableValues = {"ASC", "DESC"})
    private String orderDirection = "DESC";
}
