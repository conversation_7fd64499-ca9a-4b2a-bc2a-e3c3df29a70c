// app.js
import { request } from './utils/request'
import { getStorageSync, setStorageSync, removeStorageSync } from './utils/storage'

App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'https://api.studentmis.tsinghua.edu.cn',
    version: '2.0.0',
    systemInfo: null,
    isIOS: false,
    statusBarHeight: 0,
    navBarHeight: 0,
    screenHeight: 0,
    windowHeight: 0
  },

  onLaunch(options) {
    console.log('StudentMIS 小程序启动', options)
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查更新
    this.checkForUpdate()
    
    // 初始化用户信息
    this.initUserInfo()
    
    // 初始化网络监听
    this.initNetworkListener()
    
    // 统计启动信息
    this.reportLaunch(options)
  },

  onShow(options) {
    console.log('小程序显示', options)
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 更新在线状态
    this.updateOnlineStatus(true)
  },

  onHide() {
    console.log('小程序隐藏')
    
    // 更新在线状态
    this.updateOnlineStatus(false)
  },

  onError(error) {
    console.error('小程序错误:', error)
    
    // 错误上报
    this.reportError(error)
  },

  onUnhandledRejection(res) {
    console.error('未处理的Promise拒绝:', res)
    
    // 错误上报
    this.reportError(res.reason)
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.globalData.systemInfo = systemInfo
      this.globalData.isIOS = systemInfo.platform === 'ios'
      this.globalData.statusBarHeight = systemInfo.statusBarHeight
      this.globalData.screenHeight = systemInfo.screenHeight
      this.globalData.windowHeight = systemInfo.windowHeight
      
      // 计算导航栏高度
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
      this.globalData.navBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - systemInfo.statusBarHeight
      
      console.log('系统信息:', systemInfo)
    } catch (error) {
      console.error('获取系统信息失败:', error)
    }
  },

  // 检查更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate)
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请检查网络后重试',
          showCancel: false
        })
      })
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const token = getStorageSync('token')
    const userInfo = getStorageSync('userInfo')
    
    if (token) {
      this.globalData.token = token
    }
    
    if (userInfo) {
      this.globalData.userInfo = userInfo
    }
  },

  // 初始化网络监听
  initNetworkListener() {
    wx.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res)
      
      if (!res.isConnected) {
        wx.showToast({
          title: '网络连接已断开',
          icon: 'none',
          duration: 2000
        })
      } else if (res.networkType !== 'none') {
        wx.showToast({
          title: '网络已连接',
          icon: 'success',
          duration: 1500
        })
      }
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = this.globalData.token
    
    if (!token) {
      return false
    }
    
    // 验证token有效性
    request({
      url: '/auth/validate',
      method: 'POST',
      data: { token }
    }).then(res => {
      if (!res.data) {
        this.logout()
      }
    }).catch(() => {
      this.logout()
    })
    
    return true
  },

  // 登录
  login(loginData) {
    return new Promise((resolve, reject) => {
      request({
        url: '/auth/login',
        method: 'POST',
        data: loginData
      }).then(res => {
        const { token, ...userInfo } = res.data
        
        // 保存登录信息
        this.globalData.token = token
        this.globalData.userInfo = userInfo
        
        setStorageSync('token', token)
        setStorageSync('userInfo', userInfo)
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        resolve(res.data)
      }).catch(error => {
        wx.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
        reject(error)
      })
    })
  },

  // 登出
  logout() {
    // 清除本地数据
    this.globalData.token = null
    this.globalData.userInfo = null
    
    removeStorageSync('token')
    removeStorageSync('userInfo')
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 更新在线状态
  updateOnlineStatus(isOnline) {
    if (this.globalData.token) {
      request({
        url: '/user/online-status',
        method: 'PUT',
        data: { isOnline }
      }).catch(error => {
        console.error('更新在线状态失败:', error)
      })
    }
  },

  // 统计启动信息
  reportLaunch(options) {
    const launchData = {
      scene: options.scene,
      path: options.path,
      query: options.query,
      referrerInfo: options.referrerInfo,
      timestamp: Date.now(),
      version: this.globalData.version,
      systemInfo: this.globalData.systemInfo
    }
    
    request({
      url: '/analytics/launch',
      method: 'POST',
      data: launchData
    }).catch(error => {
      console.error('启动统计失败:', error)
    })
  },

  // 错误上报
  reportError(error) {
    const errorData = {
      message: error.message || error,
      stack: error.stack,
      timestamp: Date.now(),
      userAgent: this.globalData.systemInfo?.system,
      version: this.globalData.version,
      userId: this.globalData.userInfo?.userId
    }
    
    request({
      url: '/analytics/error',
      method: 'POST',
      data: errorData
    }).catch(err => {
      console.error('错误上报失败:', err)
    })
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo
  },

  // 获取Token
  getToken() {
    return this.globalData.token
  },

  // 检查是否已登录
  isLoggedIn() {
    return !!this.globalData.token
  },

  // 获取系统信息
  getSystemInfo() {
    return this.globalData.systemInfo
  }
})
