package edu.tsinghua.studentmis.auth.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import edu.tsinghua.studentmis.auth.dto.LoginRequest;
import edu.tsinghua.studentmis.auth.dto.LoginResponse;
import edu.tsinghua.studentmis.auth.entity.SysUser;
import edu.tsinghua.studentmis.auth.mapper.SysUserMapper;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import edu.tsinghua.studentmis.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final SysUserMapper userMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String LOGIN_FAIL_KEY = "login:fail:";
    private static final String TOKEN_KEY = "token:";
    private static final int MAX_LOGIN_FAIL_COUNT = 5;
    private static final int LOCK_TIME_MINUTES = 30;

    /**
     * 用户登录
     */
    @Transactional
    public LoginResponse login(LoginRequest request, String clientIp) {
        log.info("用户登录尝试: username={}, ip={}", request.getUsername(), clientIp);
        
        // 检查登录失败次数
        checkLoginFailCount(request.getUsername());
        
        // 查询用户
        SysUser user = userMapper.selectByUsername(request.getUsername());
        if (user == null) {
            handleLoginFail(request.getUsername(), "用户不存在");
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        // 检查用户状态
        checkUserStatus(user);
        
        // 验证密码
        if (!verifyPassword(request.getPassword(), user.getPasswordHash(), user.getSalt())) {
            handleLoginFail(request.getUsername(), "密码错误");
            throw new BusinessException(ResultCode.PASSWORD_ERROR);
        }
        
        // 检查密码是否过期
        if (user.isPasswordExpired()) {
            throw new BusinessException(ResultCode.PASSWORD_EXPIRED);
        }
        
        // 登录成功处理
        handleLoginSuccess(user, clientIp);
        
        // 生成JWT令牌
        String token = generateToken(user);
        
        // 缓存令牌
        cacheToken(user.getId(), token);
        
        log.info("用户登录成功: username={}, userId={}", user.getUsername(), user.getId());
        
        return LoginResponse.builder()
                .token(token)
                .userId(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatarUrl(user.getAvatarUrl())
                .build();
    }

    /**
     * 用户登出
     */
    public void logout(String token) {
        if (StrUtil.isBlank(token)) {
            return;
        }
        
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            String tokenKey = TOKEN_KEY + userId;
            redisTemplate.delete(tokenKey);
            log.info("用户登出成功: userId={}", userId);
        } catch (Exception e) {
            log.warn("用户登出处理异常: {}", e.getMessage());
        }
    }

    /**
     * 刷新令牌
     */
    public LoginResponse refreshToken(String token) {
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }
        
        // 验证令牌
        if (SecurityUtils.isTokenExpired(token)) {
            throw new BusinessException(ResultCode.TOKEN_EXPIRED);
        }
        
        // 获取用户信息
        Long userId = SecurityUtils.getCurrentUserId();
        SysUser user = userMapper.selectById(userId);
        if (user == null || !user.isEnabled()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        // 生成新令牌
        String newToken = generateToken(user);
        
        // 更新缓存
        cacheToken(user.getId(), newToken);
        
        log.info("令牌刷新成功: userId={}", userId);
        
        return LoginResponse.builder()
                .token(newToken)
                .userId(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatarUrl(user.getAvatarUrl())
                .build();
    }

    /**
     * 检查登录失败次数
     */
    private void checkLoginFailCount(String username) {
        String key = LOGIN_FAIL_KEY + username;
        Integer failCount = (Integer) redisTemplate.opsForValue().get(key);
        
        if (failCount != null && failCount >= MAX_LOGIN_FAIL_COUNT) {
            throw new BusinessException(ResultCode.USER_LOCKED, 
                String.format("登录失败次数过多，账户已被锁定%d分钟", LOCK_TIME_MINUTES));
        }
    }

    /**
     * 检查用户状态
     */
    private void checkUserStatus(SysUser user) {
        if (!user.isEnabled()) {
            if (user.isLocked()) {
                throw new BusinessException(ResultCode.USER_LOCKED);
            } else {
                throw new BusinessException(ResultCode.USER_DISABLED);
            }
        }
    }

    /**
     * 验证密码
     */
    private boolean verifyPassword(String rawPassword, String hashedPassword, String salt) {
        return BCrypt.checkpw(rawPassword + salt, hashedPassword);
    }

    /**
     * 处理登录失败
     */
    private void handleLoginFail(String username, String reason) {
        String key = LOGIN_FAIL_KEY + username;
        Integer failCount = (Integer) redisTemplate.opsForValue().get(key);
        failCount = failCount == null ? 1 : failCount + 1;
        
        redisTemplate.opsForValue().set(key, failCount, LOCK_TIME_MINUTES, TimeUnit.MINUTES);
        
        log.warn("用户登录失败: username={}, reason={}, failCount={}", username, reason, failCount);
    }

    /**
     * 处理登录成功
     */
    private void handleLoginSuccess(SysUser user, String clientIp) {
        // 清除登录失败记录
        String failKey = LOGIN_FAIL_KEY + user.getUsername();
        redisTemplate.delete(failKey);
        
        // 更新用户最后登录信息
        user.updateLastLoginInfo(clientIp);
        userMapper.updateById(user);
    }

    /**
     * 生成JWT令牌
     */
    private String generateToken(SysUser user) {
        // 这里简化处理，实际应该查询用户角色
        String role = "USER"; // 默认角色
        
        return SecurityUtils.generateToken(
            user.getId(),
            user.getUsername(),
            user.getRealName(),
            role
        );
    }

    /**
     * 缓存令牌
     */
    private void cacheToken(Long userId, String token) {
        String key = TOKEN_KEY + userId;
        redisTemplate.opsForValue().set(key, token, 24, TimeUnit.HOURS);
    }
}
