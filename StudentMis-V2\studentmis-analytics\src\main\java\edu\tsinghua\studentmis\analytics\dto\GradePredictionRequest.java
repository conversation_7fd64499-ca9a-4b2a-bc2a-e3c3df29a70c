package edu.tsinghua.studentmis.analytics.dto;

import edu.tsinghua.studentmis.analytics.entity.GradePrediction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 成绩预测请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "成绩预测请求")
public class GradePredictionRequest {

    @Schema(description = "学生ID", required = true)
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @Schema(description = "课程安排ID", required = true)
    @NotNull(message = "课程安排ID不能为空")
    private Long scheduleId;

    @Schema(description = "预测类型", required = true)
    @NotNull(message = "预测类型不能为空")
    private GradePrediction.PredictionType predictionType;

    @Schema(description = "是否使用高级模型", defaultValue = "false")
    private Boolean useAdvancedModel = false;

    @Schema(description = "是否包含学习行为分析", defaultValue = "true")
    private Boolean includeBehaviorAnalysis = true;
}
