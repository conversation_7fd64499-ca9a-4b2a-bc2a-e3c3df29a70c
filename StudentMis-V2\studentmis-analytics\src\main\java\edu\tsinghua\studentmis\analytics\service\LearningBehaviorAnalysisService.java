package edu.tsinghua.studentmis.analytics.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import edu.tsinghua.studentmis.analytics.dto.LearningBehaviorAnalysisRequest;
import edu.tsinghua.studentmis.analytics.dto.LearningBehaviorAnalysisResult;
import edu.tsinghua.studentmis.analytics.entity.LearningBehaviorAnalysis;
import edu.tsinghua.studentmis.analytics.mapper.LearningBehaviorAnalysisMapper;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习行为分析服务
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LearningBehaviorAnalysisService {

    private final LearningBehaviorAnalysisMapper behaviorAnalysisMapper;

    /**
     * 分析学生学习行为
     */
    public LearningBehaviorAnalysisResult analyzeLearningBehavior(LearningBehaviorAnalysisRequest request) {
        try {
            log.info("开始分析学生学习行为: studentId={}, semesterId={}", 
                    request.getStudentId(), request.getSemesterId());

            // 收集学习行为数据
            Map<String, Object> behaviorData = collectBehaviorData(request.getStudentId(), request.getSemesterId());

            // 计算各项指标
            LearningBehaviorAnalysisResult result = calculateBehaviorMetrics(behaviorData);
            result.setStudentId(request.getStudentId());
            result.setSemesterId(request.getSemesterId());

            // 生成学习模式分析
            String learningPattern = analyzeLearningPattern(result);
            result.setLearningPattern(learningPattern);

            // 评估参与度等级
            String engagementLevel = evaluateEngagementLevel(result);
            result.setEngagementLevel(engagementLevel);

            // 评估风险等级
            String riskLevel = evaluateRiskLevel(result);
            result.setRiskLevel(riskLevel);

            // 生成改进建议
            List<String> suggestions = generateImprovementSuggestions(result);
            result.setImprovementSuggestions(suggestions);

            // 保存分析结果
            saveBehaviorAnalysis(result);

            log.info("学习行为分析完成: studentId={}, engagementLevel={}, riskLevel={}", 
                    request.getStudentId(), engagementLevel, riskLevel);

            return result;

        } catch (Exception e) {
            log.error("学习行为分析失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.ANALYTICS_DATA_ERROR, "学习行为分析失败: " + e.getMessage());
        }
    }

    /**
     * 收集学习行为数据
     */
    private Map<String, Object> collectBehaviorData(Long studentId, Long semesterId) {
        Map<String, Object> data = new HashMap<>();

        // 登录行为数据
        List<Map<String, Object>> loginData = behaviorAnalysisMapper.getLoginBehaviorData(studentId, semesterId);
        data.put("loginData", loginData);

        // 学习时长数据
        List<Map<String, Object>> studyTimeData = behaviorAnalysisMapper.getStudyTimeData(studentId, semesterId);
        data.put("studyTimeData", studyTimeData);

        // 作业完成数据
        List<Map<String, Object>> assignmentData = behaviorAnalysisMapper.getAssignmentData(studentId, semesterId);
        data.put("assignmentData", assignmentData);

        // 出勤数据
        List<Map<String, Object>> attendanceData = behaviorAnalysisMapper.getAttendanceData(studentId, semesterId);
        data.put("attendanceData", attendanceData);

        // 论坛参与数据
        List<Map<String, Object>> forumData = behaviorAnalysisMapper.getForumParticipationData(studentId, semesterId);
        data.put("forumData", forumData);

        // 资源访问数据
        List<Map<String, Object>> resourceData = behaviorAnalysisMapper.getResourceAccessData(studentId, semesterId);
        data.put("resourceData", resourceData);

        // 测验数据
        List<Map<String, Object>> quizData = behaviorAnalysisMapper.getQuizData(studentId, semesterId);
        data.put("quizData", quizData);

        return data;
    }

    /**
     * 计算行为指标
     */
    @SuppressWarnings("unchecked")
    private LearningBehaviorAnalysisResult calculateBehaviorMetrics(Map<String, Object> behaviorData) {
        LearningBehaviorAnalysisResult result = new LearningBehaviorAnalysisResult();

        // 计算登录频次
        List<Map<String, Object>> loginData = (List<Map<String, Object>>) behaviorData.get("loginData");
        int loginFrequency = loginData != null ? loginData.size() : 0;
        result.setLoginFrequency(loginFrequency);

        // 计算学习时长
        List<Map<String, Object>> studyTimeData = (List<Map<String, Object>>) behaviorData.get("studyTimeData");
        int totalStudyDuration = studyTimeData != null ? 
                studyTimeData.stream()
                        .mapToInt(data -> ((Number) data.getOrDefault("duration", 0)).intValue())
                        .sum() : 0;
        result.setStudyDuration(totalStudyDuration);

        // 计算作业完成率
        List<Map<String, Object>> assignmentData = (List<Map<String, Object>>) behaviorData.get("assignmentData");
        BigDecimal assignmentCompletionRate = calculateCompletionRate(assignmentData);
        result.setAssignmentCompletionRate(assignmentCompletionRate);

        // 计算出勤率
        List<Map<String, Object>> attendanceData = (List<Map<String, Object>>) behaviorData.get("attendanceData");
        BigDecimal attendanceRate = calculateAttendanceRate(attendanceData);
        result.setAttendanceRate(attendanceRate);

        // 计算论坛参与次数
        List<Map<String, Object>> forumData = (List<Map<String, Object>>) behaviorData.get("forumData");
        int forumParticipation = forumData != null ? forumData.size() : 0;
        result.setForumParticipation(forumParticipation);

        // 计算资源访问次数
        List<Map<String, Object>> resourceData = (List<Map<String, Object>>) behaviorData.get("resourceData");
        int resourceAccessCount = resourceData != null ? resourceData.size() : 0;
        result.setResourceAccessCount(resourceAccessCount);

        // 计算测验尝试次数
        List<Map<String, Object>> quizData = (List<Map<String, Object>>) behaviorData.get("quizData");
        int quizAttempts = quizData != null ? quizData.size() : 0;
        result.setQuizAttempts(quizAttempts);

        // 计算求助频次（基于论坛提问等）
        int helpSeekingFrequency = calculateHelpSeekingFrequency(forumData);
        result.setHelpSeekingFrequency(helpSeekingFrequency);

        // 计算同伴互动得分
        BigDecimal peerInteractionScore = calculatePeerInteractionScore(forumData);
        result.setPeerInteractionScore(peerInteractionScore);

        result.setAnalysisDate(LocalDate.now());

        return result;
    }

    /**
     * 计算完成率
     */
    private BigDecimal calculateCompletionRate(List<Map<String, Object>> assignmentData) {
        if (assignmentData == null || assignmentData.isEmpty()) {
            return BigDecimal.ZERO;
        }

        long completedCount = assignmentData.stream()
                .mapToLong(data -> {
                    Object status = data.get("status");
                    return "COMPLETED".equals(status) ? 1L : 0L;
                })
                .sum();

        double rate = (double) completedCount / assignmentData.size() * 100;
        return BigDecimal.valueOf(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算出勤率
     */
    private BigDecimal calculateAttendanceRate(List<Map<String, Object>> attendanceData) {
        if (attendanceData == null || attendanceData.isEmpty()) {
            return BigDecimal.ZERO;
        }

        long presentCount = attendanceData.stream()
                .mapToLong(data -> {
                    Object status = data.get("status");
                    return "PRESENT".equals(status) ? 1L : 0L;
                })
                .sum();

        double rate = (double) presentCount / attendanceData.size() * 100;
        return BigDecimal.valueOf(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算求助频次
     */
    private int calculateHelpSeekingFrequency(List<Map<String, Object>> forumData) {
        if (forumData == null) {
            return 0;
        }

        return (int) forumData.stream()
                .filter(data -> "QUESTION".equals(data.get("type")))
                .count();
    }

    /**
     * 计算同伴互动得分
     */
    private BigDecimal calculatePeerInteractionScore(List<Map<String, Object>> forumData) {
        if (forumData == null || forumData.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 基于回复数量、点赞数等计算互动得分
        double score = forumData.stream()
                .mapToDouble(data -> {
                    int replies = ((Number) data.getOrDefault("replies", 0)).intValue();
                    int likes = ((Number) data.getOrDefault("likes", 0)).intValue();
                    return replies * 2.0 + likes * 1.0; // 回复权重更高
                })
                .sum();

        // 标准化到0-100分
        score = Math.min(100, score / forumData.size() * 10);
        return BigDecimal.valueOf(score).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 分析学习模式
     */
    private String analyzeLearningPattern(LearningBehaviorAnalysisResult result) {
        // 基于各项指标分析学习模式
        if (result.getLoginFrequency() > 20 && result.getStudyDuration() > 1000) {
            if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(90)) > 0) {
                return "勤奋型学习者";
            } else {
                return "活跃但效率待提升";
            }
        } else if (result.getLoginFrequency() < 10) {
            return "低活跃度学习者";
        } else if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(95)) > 0) {
            return "高效型学习者";
        } else if (result.getForumParticipation() > 10) {
            return "互动型学习者";
        } else {
            return "常规型学习者";
        }
    }

    /**
     * 评估参与度等级
     */
    private String evaluateEngagementLevel(LearningBehaviorAnalysisResult result) {
        int score = 0;

        // 登录频次评分
        if (result.getLoginFrequency() > 25) score += 3;
        else if (result.getLoginFrequency() > 15) score += 2;
        else if (result.getLoginFrequency() > 5) score += 1;

        // 学习时长评分
        if (result.getStudyDuration() > 1500) score += 3;
        else if (result.getStudyDuration() > 800) score += 2;
        else if (result.getStudyDuration() > 300) score += 1;

        // 作业完成率评分
        if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(90)) > 0) score += 3;
        else if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(70)) > 0) score += 2;
        else if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(50)) > 0) score += 1;

        // 论坛参与评分
        if (result.getForumParticipation() > 15) score += 2;
        else if (result.getForumParticipation() > 5) score += 1;

        if (score >= 8) return "HIGH";
        else if (score >= 5) return "MEDIUM";
        else return "LOW";
    }

    /**
     * 评估风险等级
     */
    private String evaluateRiskLevel(LearningBehaviorAnalysisResult result) {
        int riskScore = 0;

        // 出勤率风险
        if (result.getAttendanceRate().compareTo(BigDecimal.valueOf(70)) < 0) riskScore += 3;
        else if (result.getAttendanceRate().compareTo(BigDecimal.valueOf(85)) < 0) riskScore += 1;

        // 作业完成率风险
        if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(60)) < 0) riskScore += 3;
        else if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(80)) < 0) riskScore += 1;

        // 学习活跃度风险
        if (result.getLoginFrequency() < 5) riskScore += 2;
        else if (result.getLoginFrequency() < 10) riskScore += 1;

        // 学习时长风险
        if (result.getStudyDuration() < 200) riskScore += 2;
        else if (result.getStudyDuration() < 500) riskScore += 1;

        if (riskScore >= 6) return "HIGH";
        else if (riskScore >= 3) return "MEDIUM";
        else return "LOW";
    }

    /**
     * 生成改进建议
     */
    private List<String> generateImprovementSuggestions(LearningBehaviorAnalysisResult result) {
        List<String> suggestions = new ArrayList<>();

        // 基于出勤率的建议
        if (result.getAttendanceRate().compareTo(BigDecimal.valueOf(80)) < 0) {
            suggestions.add("建议提高课堂出勤率，积极参与课堂讨论");
        }

        // 基于作业完成率的建议
        if (result.getAssignmentCompletionRate().compareTo(BigDecimal.valueOf(80)) < 0) {
            suggestions.add("建议按时完成作业，如有困难可寻求老师或同学帮助");
        }

        // 基于学习时长的建议
        if (result.getStudyDuration() < 500) {
            suggestions.add("建议增加学习时间，制定合理的学习计划");
        }

        // 基于论坛参与的建议
        if (result.getForumParticipation() < 5) {
            suggestions.add("建议积极参与课程论坛讨论，与同学交流学习心得");
        }

        // 基于登录频次的建议
        if (result.getLoginFrequency() < 10) {
            suggestions.add("建议定期登录学习平台，及时获取课程信息和资料");
        }

        // 基于风险等级的建议
        if ("HIGH".equals(result.getRiskLevel())) {
            suggestions.add("学习状态需要重点关注，建议主动联系任课老师或学业导师");
        }

        return suggestions;
    }

    /**
     * 获取最新的行为分析结果
     */
    public LearningBehaviorAnalysis getLatestBehaviorAnalysis(Long studentId) {
        return behaviorAnalysisMapper.getLatestByStudentId(studentId);
    }

    /**
     * 保存行为分析结果
     */
    private void saveBehaviorAnalysis(LearningBehaviorAnalysisResult result) {
        LearningBehaviorAnalysis analysis = new LearningBehaviorAnalysis();
        analysis.setStudentId(result.getStudentId());
        analysis.setSemesterId(result.getSemesterId());
        analysis.setLoginFrequency(result.getLoginFrequency());
        analysis.setStudyDuration(result.getStudyDuration());
        analysis.setAssignmentCompletionRate(result.getAssignmentCompletionRate());
        analysis.setAttendanceRate(result.getAttendanceRate());
        analysis.setForumParticipation(result.getForumParticipation());
        analysis.setResourceAccessCount(result.getResourceAccessCount());
        analysis.setQuizAttempts(result.getQuizAttempts());
        analysis.setHelpSeekingFrequency(result.getHelpSeekingFrequency());
        analysis.setPeerInteractionScore(result.getPeerInteractionScore());
        analysis.setLearningPattern(result.getLearningPattern());
        analysis.setEngagementLevel(result.getEngagementLevel());
        analysis.setRiskLevel(result.getRiskLevel());
        analysis.setAnalysisDate(result.getAnalysisDate());

        behaviorAnalysisMapper.insert(analysis);
    }
}
