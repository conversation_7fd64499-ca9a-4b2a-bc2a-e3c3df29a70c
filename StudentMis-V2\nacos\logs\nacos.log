2025-06-19 19:26:04,863 INFO Starting Nacos v2.3.0 using Java 17.0.15 on DESKTOP-620EO8D with PID 5236 (D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\target\nacos-server.jar started by jsxzxhx in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\bin)

2025-06-19 19:26:04,864 INFO The following 1 profile is active: "standalone"

2025-06-19 19:26:04,996 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-06-19 19:26:04,997 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-06-19 19:26:04,997 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-06-19 19:26:05,223 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-06-19 19:26:05,333 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-06-19 19:26:05,344 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-06-19 19:26:05,355 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,646 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-06-19 19:26:07,519 INFO Tomcat initialized with port(s): 8848 (http)

2025-06-19 19:26:07,721 INFO Starting service [Tomcat]

2025-06-19 19:26:07,722 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-06-19 19:26:07,831 INFO Initializing Spring embedded WebApplicationContext

2025-06-19 19:26:07,831 INFO Root WebApplicationContext: initialization completed in 2910 ms

2025-06-19 19:26:08,098 INFO Nacos-related cluster resource initialization

2025-06-19 19:26:08,106 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:26:08,106 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:26:08,109 INFO The cluster resource is initialized

2025-06-19 19:26:08,475 INFO HikariPool-1 - Starting...

2025-06-19 19:26:08,483 WARN Registered driver with driverClassName=org.apache.derby.jdbc.EmbeddedDriver was not found, trying direct instantiation.

2025-06-19 19:26:09,179 INFO HikariPool-1 - Driver does not support get/set network timeout for connections. (Feature not implemented: No details.)

2025-06-19 19:26:09,182 INFO HikariPool-1 - Start completed.

2025-06-19 19:26:10,109 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-06-19 19:26:10,232 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info

2025-06-19 19:26:10,299 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_beta

2025-06-19 19:26:10,311 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_tag

2025-06-19 19:26:10,319 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_aggr

2025-06-19 19:26:10,426 INFO Fail to find connection runtime ejector for name nacos,use default

2025-06-19 19:26:10,505 INFO Not configure type of control plugin, no limit control for current node.

2025-06-19 19:26:10,507 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@5a6d30e2, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@a098d76]

2025-06-19 19:26:10,508 INFO No connection rule content found ,use default empty rule 

2025-06-19 19:26:10,511 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:10,518 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-06-19 19:26:10,518 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,518 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-06-19 19:26:10,518 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,519 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-06-19 19:26:10,519 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,559 INFO Ready to get current node abilities...

2025-06-19 19:26:10,562 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT, SERVER, CLUSTER_CLIENT]

2025-06-19 19:26:10,562 INFO Initialize current abilities finish...

2025-06-19 19:26:10,563 INFO Ready to get current node abilities...

2025-06-19 19:26:10,564 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-06-19 19:26:10,564 INFO Initialize current abilities finish...

2025-06-19 19:26:10,564 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-06-19 19:26:11,442 INFO Connection check task start

2025-06-19 19:26:11,448 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:11,448 INFO Out dated connection ,size=0

2025-06-19 19:26:11,448 INFO Connection check task end

2025-06-19 19:26:12,398 INFO Adding welcome page: class path resource [static/index.html]

2025-06-19 19:26:12,843 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-06-19 19:26:12,844 INFO Will not secure Ant [pattern='/**']

2025-06-19 19:26:12,871 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@60f77af, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2574a9e3, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d4d8579, org.springframework.security.web.header.HeaderWriterFilter@6f9e08d4, org.springframework.security.web.csrf.CsrfFilter@1f992a3a, org.springframework.security.web.authentication.logout.LogoutFilter@412c5e8b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@29bcf51d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3b24087d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@18b6d3c1, org.springframework.security.web.session.SessionManagementFilter@2b08772d, org.springframework.security.web.access.ExceptionTranslationFilter@30bf26df]

2025-06-19 19:26:12,909 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-06-19 19:26:12,964 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-06-19 19:26:12,982 INFO No tps control rule of HttpHealthCheck found  

2025-06-19 19:26:12,982 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,982 INFO No tps control rule of NamingInstanceRegister found  

2025-06-19 19:26:12,982 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingServiceSubscribe found  

2025-06-19 19:26:12,983 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingInstanceMetadataUpdate found  

2025-06-19 19:26:12,983 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingServiceQuery found  

2025-06-19 19:26:12,983 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingInstanceDeregister found  

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingServiceUpdate found  

2025-06-19 19:26:12,984 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingServiceDeregister found  

2025-06-19 19:26:12,984 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingInstanceUpdate found  

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of ConfigPublish found  

2025-06-19 19:26:12,985 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingServiceRegister found  

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingInstanceQuery found  

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingServiceListQuery found  

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of ConfigQuery found  

2025-06-19 19:26:12,986 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-06-19 19:26:12,987 WARN Tps point for ClusterConfigChangeNotify registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ConfigListen found  

2025-06-19 19:26:12,987 WARN Tps point for ConfigListen registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ConfigRemove found  

2025-06-19 19:26:12,987 WARN Tps point for ConfigRemove registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of HealthCheck found  

2025-06-19 19:26:12,987 WARN Tps point for HealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingServiceQuery found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingInstanceBatchRegister found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingInstanceBatchRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingInstanceRegisterDeregister found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 INFO No tps control rule of RemoteNamingServiceListQuery found  

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 INFO No tps control rule of RemoteNamingServiceSubscribeUnSubscribe found  

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingServiceSubscribeUnSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,993 INFO Started Nacos in 9.176 seconds (JVM running for 9.67)

2025-06-19 19:26:12,994 INFO Nacos started successfully in stand alone mode. use embedded storage

2025-06-19 19:26:13,514 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:14,459 INFO Connection check task start

2025-06-19 19:26:14,459 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:14,459 INFO Out dated connection ,size=0

2025-06-19 19:26:14,459 INFO Connection check task end

2025-06-19 19:26:16,524 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:17,472 INFO Connection check task start

2025-06-19 19:26:17,472 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:17,472 INFO Out dated connection ,size=0

2025-06-19 19:26:17,472 INFO Connection check task end

2025-06-19 19:26:19,540 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:20,484 INFO Connection check task start

2025-06-19 19:26:20,484 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:20,484 INFO Out dated connection ,size=0

2025-06-19 19:26:20,484 INFO Connection check task end

2025-06-19 19:26:22,544 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:23,497 INFO Connection check task start

2025-06-19 19:26:23,497 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:23,497 INFO Out dated connection ,size=0

2025-06-19 19:26:23,497 INFO Connection check task end

2025-06-19 19:26:25,558 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:26,506 INFO Connection check task start

2025-06-19 19:26:26,506 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:26,506 INFO Out dated connection ,size=0

2025-06-19 19:26:26,506 INFO Connection check task end

2025-06-19 19:26:28,569 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:29,515 INFO Connection check task start

2025-06-19 19:26:29,515 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:29,515 INFO Out dated connection ,size=0

2025-06-19 19:26:29,515 INFO Connection check task end

2025-06-19 19:26:31,569 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:32,525 INFO Connection check task start

2025-06-19 19:26:32,525 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:32,525 INFO Out dated connection ,size=0

2025-06-19 19:26:32,525 INFO Connection check task end

2025-06-19 19:26:34,575 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:35,539 INFO Connection check task start

2025-06-19 19:26:35,539 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:35,539 INFO Out dated connection ,size=0

2025-06-19 19:26:35,539 INFO Connection check task end

2025-06-19 19:26:37,586 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:38,547 INFO Connection check task start

2025-06-19 19:26:38,547 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:38,548 INFO Out dated connection ,size=0

2025-06-19 19:26:38,548 INFO Connection check task end

2025-06-19 19:26:40,594 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:41,553 INFO Connection check task start

2025-06-19 19:26:41,553 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:41,553 INFO Out dated connection ,size=0

2025-06-19 19:26:41,553 INFO Connection check task end

2025-06-19 19:26:43,604 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:44,567 INFO Connection check task start

2025-06-19 19:26:44,567 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:44,567 INFO Out dated connection ,size=0

2025-06-19 19:26:44,567 INFO Connection check task end

2025-06-19 19:26:46,617 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:47,568 INFO Connection check task start

2025-06-19 19:26:47,568 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:47,568 INFO Out dated connection ,size=0

2025-06-19 19:26:47,568 INFO Connection check task end

2025-06-19 19:26:49,625 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:50,573 INFO Connection check task start

2025-06-19 19:26:50,573 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:50,573 INFO Out dated connection ,size=0

2025-06-19 19:26:50,573 INFO Connection check task end

2025-06-19 19:26:52,634 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:53,588 INFO Connection check task start

2025-06-19 19:26:53,588 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:53,588 INFO Out dated connection ,size=0

2025-06-19 19:26:53,588 INFO Connection check task end

2025-06-19 19:26:55,639 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:56,593 INFO Connection check task start

2025-06-19 19:26:56,593 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:56,593 INFO Out dated connection ,size=0

2025-06-19 19:26:56,593 INFO Connection check task end

2025-06-19 19:26:58,651 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:59,596 INFO Connection check task start

2025-06-19 19:26:59,596 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:59,596 INFO Out dated connection ,size=0

2025-06-19 19:26:59,596 INFO Connection check task end

2025-06-19 19:27:01,663 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:02,612 INFO Connection check task start

2025-06-19 19:27:02,612 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:02,612 INFO Out dated connection ,size=0

2025-06-19 19:27:02,612 INFO Connection check task end

2025-06-19 19:27:04,670 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:05,629 INFO Connection check task start

2025-06-19 19:27:05,629 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:05,629 INFO Out dated connection ,size=0

2025-06-19 19:27:05,629 INFO Connection check task end

2025-06-19 19:27:07,677 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:08,632 INFO Connection check task start

2025-06-19 19:27:08,632 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:08,632 INFO Out dated connection ,size=0

2025-06-19 19:27:08,632 INFO Connection check task end

2025-06-19 19:27:10,690 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:11,639 INFO Connection check task start

2025-06-19 19:27:11,639 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:11,639 INFO Out dated connection ,size=0

2025-06-19 19:27:11,639 INFO Connection check task end

2025-06-19 19:27:13,691 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:14,649 INFO Connection check task start

2025-06-19 19:27:14,649 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:14,649 INFO Out dated connection ,size=0

2025-06-19 19:27:14,649 INFO Connection check task end

2025-06-19 19:27:16,692 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:17,663 INFO Connection check task start

2025-06-19 19:27:17,663 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:17,663 INFO Out dated connection ,size=0

2025-06-19 19:27:17,663 INFO Connection check task end

2025-06-19 19:27:49,724 INFO Starting Nacos v2.3.0 using Java 17.0.15 on DESKTOP-620EO8D with PID 9160 (D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\target\nacos-server.jar started by jsxzxhx in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\bin)

2025-06-19 19:27:49,725 INFO The following 1 profile is active: "standalone"

2025-06-19 19:27:49,862 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-06-19 19:27:49,862 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-06-19 19:27:49,863 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-06-19 19:27:50,060 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-06-19 19:27:50,162 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-06-19 19:27:50,174 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-06-19 19:27:50,185 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,477 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-06-19 19:27:52,397 INFO Tomcat initialized with port(s): 8848 (http)

2025-06-19 19:27:52,597 INFO Starting service [Tomcat]

2025-06-19 19:27:52,597 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-06-19 19:27:52,708 INFO Initializing Spring embedded WebApplicationContext

2025-06-19 19:27:52,708 INFO Root WebApplicationContext: initialization completed in 2922 ms

2025-06-19 19:27:52,969 INFO Nacos-related cluster resource initialization

2025-06-19 19:27:52,976 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:27:52,977 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:27:52,979 INFO The cluster resource is initialized

2025-06-19 19:27:53,364 INFO HikariPool-1 - Starting...

2025-06-19 19:27:53,372 WARN Registered driver with driverClassName=org.apache.derby.jdbc.EmbeddedDriver was not found, trying direct instantiation.

2025-06-19 19:27:53,785 INFO HikariPool-1 - Driver does not support get/set network timeout for connections. (Feature not implemented: No details.)

2025-06-19 19:27:53,787 INFO HikariPool-1 - Start completed.

2025-06-19 19:27:54,709 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-06-19 19:27:54,709 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-06-19 19:27:54,709 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-06-19 19:27:54,830 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info

2025-06-19 19:27:54,884 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_beta

2025-06-19 19:27:54,895 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_tag

2025-06-19 19:27:54,903 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_aggr

2025-06-19 19:27:55,016 INFO Fail to find connection runtime ejector for name nacos,use default

2025-06-19 19:27:55,114 INFO Not configure type of control plugin, no limit control for current node.

2025-06-19 19:27:55,116 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@cb7fa71, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@4b6e1c0]

2025-06-19 19:27:55,117 INFO No connection rule content found ,use default empty rule 

2025-06-19 19:27:55,121 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:55,129 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-06-19 19:27:55,130 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-06-19 19:27:55,130 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-06-19 19:27:55,130 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-06-19 19:27:55,130 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-06-19 19:27:55,130 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-06-19 19:27:55,168 INFO Ready to get current node abilities...

2025-06-19 19:27:55,171 INFO Ready to initialize current node abilities, support modes: [CLUSTER_CLIENT, SDK_CLIENT, SERVER]

2025-06-19 19:27:55,172 INFO Initialize current abilities finish...

2025-06-19 19:27:55,173 INFO Ready to get current node abilities...

2025-06-19 19:27:55,173 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-06-19 19:27:55,173 INFO Initialize current abilities finish...

2025-06-19 19:27:55,174 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-06-19 19:27:56,033 INFO Connection check task start

2025-06-19 19:27:56,038 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:56,038 INFO Out dated connection ,size=0

2025-06-19 19:27:56,038 INFO Connection check task end

2025-06-19 19:27:56,859 INFO Adding welcome page: class path resource [static/index.html]

2025-06-19 19:27:57,328 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-06-19 19:27:57,329 INFO Will not secure Ant [pattern='/**']

2025-06-19 19:27:57,352 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@32091c14, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c40ffef, org.springframework.security.web.context.SecurityContextPersistenceFilter@3bfae028, org.springframework.security.web.header.HeaderWriterFilter@74b86971, org.springframework.security.web.csrf.CsrfFilter@1e5eb20a, org.springframework.security.web.authentication.logout.LogoutFilter@4391a2d8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47829d6d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a1b8a46, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@286855ea, org.springframework.security.web.session.SessionManagementFilter@1ca610a0, org.springframework.security.web.access.ExceptionTranslationFilter@4538856f]

2025-06-19 19:27:57,381 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-06-19 19:27:57,424 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-06-19 19:27:57,440 INFO No tps control rule of NamingServiceRegister found  

2025-06-19 19:27:57,440 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,440 INFO No tps control rule of NamingServiceQuery found  

2025-06-19 19:27:57,441 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of HttpHealthCheck found  

2025-06-19 19:27:57,441 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of NamingServiceListQuery found  

2025-06-19 19:27:57,441 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of NamingInstanceMetadataUpdate found  

2025-06-19 19:27:57,441 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of NamingInstanceQuery found  

2025-06-19 19:27:57,441 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of NamingServiceSubscribe found  

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of NamingInstanceUpdate found  

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of NamingInstanceDeregister found  

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of ConfigQuery found  

2025-06-19 19:27:57,442 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 INFO No tps control rule of NamingInstanceRegister found  

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 INFO No tps control rule of NamingServiceDeregister found  

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 INFO No tps control rule of NamingServiceUpdate found  

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,444 INFO No tps control rule of ConfigPublish found  

2025-06-19 19:27:57,444 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,444 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,444 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-06-19 19:27:57,444 WARN Tps point for ClusterConfigChangeNotify registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of ConfigListen found  

2025-06-19 19:27:57,445 WARN Tps point for ConfigListen registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of ConfigRemove found  

2025-06-19 19:27:57,445 WARN Tps point for ConfigRemove registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of HealthCheck found  

2025-06-19 19:27:57,445 WARN Tps point for HealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of RemoteNamingServiceQuery found  

2025-06-19 19:27:57,445 WARN Tps point for RemoteNamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingInstanceBatchRegister found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingInstanceBatchRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingInstanceRegisterDeregister found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingServiceListQuery found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingServiceSubscribeUnSubscribe found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingServiceSubscribeUnSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,450 INFO Started Nacos in 8.783 seconds (JVM running for 9.26)

2025-06-19 19:27:57,450 INFO Nacos started successfully in stand alone mode. use embedded storage

2025-06-19 19:27:58,135 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:59,052 INFO Connection check task start

2025-06-19 19:27:59,052 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:59,052 INFO Out dated connection ,size=0

2025-06-19 19:27:59,052 INFO Connection check task end

2025-06-19 19:28:01,136 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:02,052 INFO Connection check task start

2025-06-19 19:28:02,052 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:02,052 INFO Out dated connection ,size=0

2025-06-19 19:28:02,052 INFO Connection check task end

2025-06-19 19:28:04,139 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:05,069 INFO Connection check task start

2025-06-19 19:28:05,069 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:05,069 INFO Out dated connection ,size=0

2025-06-19 19:28:05,069 INFO Connection check task end

2025-06-19 19:28:07,149 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:08,082 INFO Connection check task start

2025-06-19 19:28:08,082 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:08,082 INFO Out dated connection ,size=0

2025-06-19 19:28:08,082 INFO Connection check task end

2025-06-19 19:28:10,163 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:11,093 INFO Connection check task start

2025-06-19 19:28:11,093 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:11,093 INFO Out dated connection ,size=0

2025-06-19 19:28:11,093 INFO Connection check task end

2025-06-19 19:28:13,164 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:14,104 INFO Connection check task start

2025-06-19 19:28:14,104 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:14,104 INFO Out dated connection ,size=0

2025-06-19 19:28:14,104 INFO Connection check task end

2025-06-19 19:28:16,169 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:17,117 INFO Connection check task start

2025-06-19 19:28:17,117 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:17,117 INFO Out dated connection ,size=0

2025-06-19 19:28:17,117 INFO Connection check task end

2025-06-19 19:28:19,180 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:20,119 INFO Connection check task start

2025-06-19 19:28:20,119 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:20,119 INFO Out dated connection ,size=0

2025-06-19 19:28:20,119 INFO Connection check task end

2025-06-19 19:28:22,189 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:23,127 INFO Connection check task start

2025-06-19 19:28:23,127 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:23,127 INFO Out dated connection ,size=0

2025-06-19 19:28:23,127 INFO Connection check task end

2025-06-19 19:28:25,202 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:26,135 INFO Connection check task start

2025-06-19 19:28:26,135 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:26,135 INFO Out dated connection ,size=0

2025-06-19 19:28:26,135 INFO Connection check task end

2025-06-19 19:28:28,204 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:29,136 INFO Connection check task start

2025-06-19 19:28:29,136 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:29,136 INFO Out dated connection ,size=0

2025-06-19 19:28:29,136 INFO Connection check task end

2025-06-19 19:28:31,212 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:32,144 INFO Connection check task start

2025-06-19 19:28:32,145 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:32,145 INFO Out dated connection ,size=0

2025-06-19 19:28:32,145 INFO Connection check task end

2025-06-19 19:28:34,217 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:35,155 INFO Connection check task start

2025-06-19 19:28:35,155 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:35,155 INFO Out dated connection ,size=0

2025-06-19 19:28:35,155 INFO Connection check task end

2025-06-19 19:28:37,223 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:38,165 INFO Connection check task start

2025-06-19 19:28:38,165 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:38,165 INFO Out dated connection ,size=0

2025-06-19 19:28:38,165 INFO Connection check task end

2025-06-19 19:28:40,225 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:41,172 INFO Connection check task start

2025-06-19 19:28:41,172 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:41,172 INFO Out dated connection ,size=0

2025-06-19 19:28:41,172 INFO Connection check task end

2025-06-19 19:28:43,239 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:44,175 INFO Connection check task start

2025-06-19 19:28:44,175 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:44,175 INFO Out dated connection ,size=0

2025-06-19 19:28:44,175 INFO Connection check task end

2025-06-19 19:28:46,241 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:47,185 INFO Connection check task start

2025-06-19 19:28:47,185 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:47,185 INFO Out dated connection ,size=0

2025-06-19 19:28:47,185 INFO Connection check task end

2025-06-19 19:28:49,253 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:50,197 INFO Connection check task start

2025-06-19 19:28:50,197 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:50,197 INFO Out dated connection ,size=0

2025-06-19 19:28:50,197 INFO Connection check task end

2025-06-19 19:28:52,254 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:53,203 INFO Connection check task start

2025-06-19 19:28:53,203 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:53,203 INFO Out dated connection ,size=0

2025-06-19 19:28:53,203 INFO Connection check task end

2025-06-19 19:28:55,267 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:56,217 INFO Connection check task start

2025-06-19 19:28:56,217 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:56,217 INFO Out dated connection ,size=0

2025-06-19 19:28:56,217 INFO Connection check task end

2025-06-19 19:28:58,270 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:59,219 INFO Connection check task start

2025-06-19 19:28:59,219 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:59,219 INFO Out dated connection ,size=0

2025-06-19 19:28:59,219 INFO Connection check task end

2025-06-19 19:29:01,285 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:02,220 INFO Connection check task start

2025-06-19 19:29:02,220 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:02,220 INFO Out dated connection ,size=0

2025-06-19 19:29:02,220 INFO Connection check task end

2025-06-19 19:29:04,288 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:05,221 INFO Connection check task start

2025-06-19 19:29:05,221 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:05,221 INFO Out dated connection ,size=0

2025-06-19 19:29:05,221 INFO Connection check task end

2025-06-19 19:29:07,290 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:08,223 INFO Connection check task start

2025-06-19 19:29:08,223 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:08,223 INFO Out dated connection ,size=0

2025-06-19 19:29:08,223 INFO Connection check task end

2025-06-19 19:29:10,304 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:11,235 INFO Connection check task start

2025-06-19 19:29:11,235 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:11,235 INFO Out dated connection ,size=0

2025-06-19 19:29:11,235 INFO Connection check task end

2025-06-19 19:29:13,309 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:14,244 INFO Connection check task start

2025-06-19 19:29:14,244 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:14,244 INFO Out dated connection ,size=0

2025-06-19 19:29:14,244 INFO Connection check task end

2025-06-19 19:29:16,318 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:17,260 INFO Connection check task start

2025-06-19 19:29:17,260 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:17,260 INFO Out dated connection ,size=0

2025-06-19 19:29:17,261 INFO Connection check task end

2025-06-19 19:29:19,328 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:20,272 INFO Connection check task start

2025-06-19 19:29:20,272 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:20,272 INFO Out dated connection ,size=0

2025-06-19 19:29:20,272 INFO Connection check task end

2025-06-19 19:29:22,334 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:23,281 INFO Connection check task start

2025-06-19 19:29:23,281 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:23,281 INFO Out dated connection ,size=0

2025-06-19 19:29:23,281 INFO Connection check task end

2025-06-19 19:29:25,336 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:26,284 INFO Connection check task start

2025-06-19 19:29:26,284 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:26,284 INFO Out dated connection ,size=0

2025-06-19 19:29:26,284 INFO Connection check task end

2025-06-19 19:29:28,352 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:29,289 INFO Connection check task start

2025-06-19 19:29:29,289 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:29,289 INFO Out dated connection ,size=0

2025-06-19 19:29:29,289 INFO Connection check task end

2025-06-19 19:29:31,367 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:32,301 INFO Connection check task start

2025-06-19 19:29:32,301 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:32,301 INFO Out dated connection ,size=0

2025-06-19 19:29:32,301 INFO Connection check task end

2025-06-19 19:29:34,381 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:35,313 INFO Connection check task start

2025-06-19 19:29:35,313 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:35,313 INFO Out dated connection ,size=0

2025-06-19 19:29:35,313 INFO Connection check task end

2025-06-19 19:29:37,386 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:38,320 INFO Connection check task start

2025-06-19 19:29:38,320 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:38,320 INFO Out dated connection ,size=0

2025-06-19 19:29:38,320 INFO Connection check task end

2025-06-19 19:29:40,388 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:41,334 INFO Connection check task start

2025-06-19 19:29:41,334 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:41,334 INFO Out dated connection ,size=0

2025-06-19 19:29:41,334 INFO Connection check task end

2025-06-19 19:29:43,394 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:44,345 INFO Connection check task start

2025-06-19 19:29:44,345 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:44,345 INFO Out dated connection ,size=0

2025-06-19 19:29:44,345 INFO Connection check task end

2025-06-19 19:29:46,406 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:47,352 INFO Connection check task start

2025-06-19 19:29:47,352 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:47,352 INFO Out dated connection ,size=0

2025-06-19 19:29:47,352 INFO Connection check task end

2025-06-19 19:29:49,407 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:50,363 INFO Connection check task start

2025-06-19 19:29:50,363 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:50,363 INFO Out dated connection ,size=0

2025-06-19 19:29:50,363 INFO Connection check task end

2025-06-19 19:29:52,419 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:53,374 INFO Connection check task start

2025-06-19 19:29:53,374 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:53,374 INFO Out dated connection ,size=0

2025-06-19 19:29:53,374 INFO Connection check task end

