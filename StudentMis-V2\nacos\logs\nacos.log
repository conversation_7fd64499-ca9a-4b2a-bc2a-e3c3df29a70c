2025-06-19 19:26:04,863 INFO Starting Nacos v2.3.0 using Java 17.0.15 on DESKTOP-620EO8D with PID 5236 (D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\target\nacos-server.jar started by jsxzxhx in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\bin)

2025-06-19 19:26:04,864 INFO The following 1 profile is active: "standalone"

2025-06-19 19:26:04,996 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-06-19 19:26:04,997 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-06-19 19:26:04,997 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-06-19 19:26:05,223 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-06-19 19:26:05,333 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-06-19 19:26:05,344 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-06-19 19:26:05,355 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,646 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-06-19 19:26:07,519 INFO Tomcat initialized with port(s): 8848 (http)

2025-06-19 19:26:07,721 INFO Starting service [Tomcat]

2025-06-19 19:26:07,722 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-06-19 19:26:07,831 INFO Initializing Spring embedded WebApplicationContext

2025-06-19 19:26:07,831 INFO Root WebApplicationContext: initialization completed in 2910 ms

2025-06-19 19:26:08,098 INFO Nacos-related cluster resource initialization

2025-06-19 19:26:08,106 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:26:08,106 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:26:08,109 INFO The cluster resource is initialized

2025-06-19 19:26:08,475 INFO HikariPool-1 - Starting...

2025-06-19 19:26:08,483 WARN Registered driver with driverClassName=org.apache.derby.jdbc.EmbeddedDriver was not found, trying direct instantiation.

2025-06-19 19:26:09,179 INFO HikariPool-1 - Driver does not support get/set network timeout for connections. (Feature not implemented: No details.)

2025-06-19 19:26:09,182 INFO HikariPool-1 - Start completed.

2025-06-19 19:26:10,109 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-06-19 19:26:10,232 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info

2025-06-19 19:26:10,299 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_beta

2025-06-19 19:26:10,311 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_tag

2025-06-19 19:26:10,319 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_aggr

2025-06-19 19:26:10,426 INFO Fail to find connection runtime ejector for name nacos,use default

2025-06-19 19:26:10,505 INFO Not configure type of control plugin, no limit control for current node.

2025-06-19 19:26:10,507 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@5a6d30e2, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@a098d76]

2025-06-19 19:26:10,508 INFO No connection rule content found ,use default empty rule 

2025-06-19 19:26:10,511 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:10,518 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-06-19 19:26:10,518 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,518 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-06-19 19:26:10,518 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,519 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-06-19 19:26:10,519 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,559 INFO Ready to get current node abilities...

2025-06-19 19:26:10,562 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT, SERVER, CLUSTER_CLIENT]

2025-06-19 19:26:10,562 INFO Initialize current abilities finish...

2025-06-19 19:26:10,563 INFO Ready to get current node abilities...

2025-06-19 19:26:10,564 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-06-19 19:26:10,564 INFO Initialize current abilities finish...

2025-06-19 19:26:10,564 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-06-19 19:26:11,442 INFO Connection check task start

2025-06-19 19:26:11,448 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:11,448 INFO Out dated connection ,size=0

2025-06-19 19:26:11,448 INFO Connection check task end

2025-06-19 19:26:12,398 INFO Adding welcome page: class path resource [static/index.html]

2025-06-19 19:26:12,843 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-06-19 19:26:12,844 INFO Will not secure Ant [pattern='/**']

2025-06-19 19:26:12,871 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@60f77af, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2574a9e3, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d4d8579, org.springframework.security.web.header.HeaderWriterFilter@6f9e08d4, org.springframework.security.web.csrf.CsrfFilter@1f992a3a, org.springframework.security.web.authentication.logout.LogoutFilter@412c5e8b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@29bcf51d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3b24087d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@18b6d3c1, org.springframework.security.web.session.SessionManagementFilter@2b08772d, org.springframework.security.web.access.ExceptionTranslationFilter@30bf26df]

2025-06-19 19:26:12,909 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-06-19 19:26:12,964 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-06-19 19:26:12,982 INFO No tps control rule of HttpHealthCheck found  

2025-06-19 19:26:12,982 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,982 INFO No tps control rule of NamingInstanceRegister found  

2025-06-19 19:26:12,982 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingServiceSubscribe found  

2025-06-19 19:26:12,983 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingInstanceMetadataUpdate found  

2025-06-19 19:26:12,983 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingServiceQuery found  

2025-06-19 19:26:12,983 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingInstanceDeregister found  

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingServiceUpdate found  

2025-06-19 19:26:12,984 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingServiceDeregister found  

2025-06-19 19:26:12,984 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingInstanceUpdate found  

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of ConfigPublish found  

2025-06-19 19:26:12,985 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingServiceRegister found  

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingInstanceQuery found  

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingServiceListQuery found  

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of ConfigQuery found  

2025-06-19 19:26:12,986 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-06-19 19:26:12,987 WARN Tps point for ClusterConfigChangeNotify registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ConfigListen found  

2025-06-19 19:26:12,987 WARN Tps point for ConfigListen registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ConfigRemove found  

2025-06-19 19:26:12,987 WARN Tps point for ConfigRemove registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of HealthCheck found  

2025-06-19 19:26:12,987 WARN Tps point for HealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingServiceQuery found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingInstanceBatchRegister found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingInstanceBatchRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingInstanceRegisterDeregister found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 INFO No tps control rule of RemoteNamingServiceListQuery found  

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 INFO No tps control rule of RemoteNamingServiceSubscribeUnSubscribe found  

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingServiceSubscribeUnSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,993 INFO Started Nacos in 9.176 seconds (JVM running for 9.67)

2025-06-19 19:26:12,994 INFO Nacos started successfully in stand alone mode. use embedded storage

2025-06-19 19:26:13,514 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:14,459 INFO Connection check task start

2025-06-19 19:26:14,459 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:14,459 INFO Out dated connection ,size=0

2025-06-19 19:26:14,459 INFO Connection check task end

2025-06-19 19:26:16,524 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:17,472 INFO Connection check task start

2025-06-19 19:26:17,472 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:17,472 INFO Out dated connection ,size=0

2025-06-19 19:26:17,472 INFO Connection check task end

2025-06-19 19:26:19,540 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:20,484 INFO Connection check task start

2025-06-19 19:26:20,484 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:20,484 INFO Out dated connection ,size=0

2025-06-19 19:26:20,484 INFO Connection check task end

2025-06-19 19:26:22,544 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:23,497 INFO Connection check task start

2025-06-19 19:26:23,497 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:23,497 INFO Out dated connection ,size=0

2025-06-19 19:26:23,497 INFO Connection check task end

2025-06-19 19:26:25,558 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:26,506 INFO Connection check task start

2025-06-19 19:26:26,506 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:26,506 INFO Out dated connection ,size=0

2025-06-19 19:26:26,506 INFO Connection check task end

2025-06-19 19:26:28,569 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:29,515 INFO Connection check task start

2025-06-19 19:26:29,515 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:29,515 INFO Out dated connection ,size=0

2025-06-19 19:26:29,515 INFO Connection check task end

2025-06-19 19:26:31,569 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:32,525 INFO Connection check task start

2025-06-19 19:26:32,525 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:32,525 INFO Out dated connection ,size=0

2025-06-19 19:26:32,525 INFO Connection check task end

2025-06-19 19:26:34,575 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:35,539 INFO Connection check task start

2025-06-19 19:26:35,539 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:35,539 INFO Out dated connection ,size=0

2025-06-19 19:26:35,539 INFO Connection check task end

2025-06-19 19:26:37,586 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:38,547 INFO Connection check task start

2025-06-19 19:26:38,547 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:38,548 INFO Out dated connection ,size=0

2025-06-19 19:26:38,548 INFO Connection check task end

2025-06-19 19:26:40,594 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:41,553 INFO Connection check task start

2025-06-19 19:26:41,553 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:41,553 INFO Out dated connection ,size=0

2025-06-19 19:26:41,553 INFO Connection check task end

2025-06-19 19:26:43,604 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:44,567 INFO Connection check task start

2025-06-19 19:26:44,567 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:44,567 INFO Out dated connection ,size=0

2025-06-19 19:26:44,567 INFO Connection check task end

2025-06-19 19:26:46,617 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:47,568 INFO Connection check task start

2025-06-19 19:26:47,568 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:47,568 INFO Out dated connection ,size=0

2025-06-19 19:26:47,568 INFO Connection check task end

2025-06-19 19:26:49,625 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:50,573 INFO Connection check task start

2025-06-19 19:26:50,573 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:50,573 INFO Out dated connection ,size=0

2025-06-19 19:26:50,573 INFO Connection check task end

2025-06-19 19:26:52,634 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:53,588 INFO Connection check task start

2025-06-19 19:26:53,588 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:53,588 INFO Out dated connection ,size=0

2025-06-19 19:26:53,588 INFO Connection check task end

2025-06-19 19:26:55,639 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:56,593 INFO Connection check task start

2025-06-19 19:26:56,593 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:56,593 INFO Out dated connection ,size=0

2025-06-19 19:26:56,593 INFO Connection check task end

2025-06-19 19:26:58,651 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:59,596 INFO Connection check task start

2025-06-19 19:26:59,596 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:59,596 INFO Out dated connection ,size=0

2025-06-19 19:26:59,596 INFO Connection check task end

