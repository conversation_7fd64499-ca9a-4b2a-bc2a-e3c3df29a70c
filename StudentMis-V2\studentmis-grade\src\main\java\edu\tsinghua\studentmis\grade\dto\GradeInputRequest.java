package edu.tsinghua.studentmis.grade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成绩录入请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "成绩录入请求")
public class GradeInputRequest {

    @Schema(description = "学生ID", required = true)
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @Schema(description = "课程安排ID", required = true)
    @NotNull(message = "课程安排ID不能为空")
    private Long scheduleId;

    @Schema(description = "平时成绩")
    @DecimalMin(value = "0", message = "平时成绩不能小于0")
    @DecimalMax(value = "100", message = "平时成绩不能大于100")
    private BigDecimal usualScore;

    @Schema(description = "期中成绩")
    @DecimalMin(value = "0", message = "期中成绩不能小于0")
    @DecimalMax(value = "100", message = "期中成绩不能大于100")
    private BigDecimal midtermScore;

    @Schema(description = "期末成绩")
    @DecimalMin(value = "0", message = "期末成绩不能小于0")
    @DecimalMax(value = "100", message = "期末成绩不能大于100")
    private BigDecimal finalScore;

    @Schema(description = "实验成绩")
    @DecimalMin(value = "0", message = "实验成绩不能小于0")
    @DecimalMax(value = "100", message = "实验成绩不能大于100")
    private BigDecimal labScore;

    @Schema(description = "作业成绩")
    @DecimalMin(value = "0", message = "作业成绩不能小于0")
    @DecimalMax(value = "100", message = "作业成绩不能大于100")
    private BigDecimal assignmentScore;

    @Schema(description = "出勤成绩")
    @DecimalMin(value = "0", message = "出勤成绩不能小于0")
    @DecimalMax(value = "100", message = "出勤成绩不能大于100")
    private BigDecimal attendanceScore;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "批量录入数据")
    private List<BatchGradeInput> batchData;

    /**
     * 批量成绩录入内部类
     */
    @Data
    @Schema(description = "批量成绩录入")
    public static class BatchGradeInput {
        
        @Schema(description = "学生ID", required = true)
        @NotNull(message = "学生ID不能为空")
        private Long studentId;

        @Schema(description = "平时成绩")
        @DecimalMin(value = "0", message = "平时成绩不能小于0")
        @DecimalMax(value = "100", message = "平时成绩不能大于100")
        private BigDecimal usualScore;

        @Schema(description = "期中成绩")
        @DecimalMin(value = "0", message = "期中成绩不能小于0")
        @DecimalMax(value = "100", message = "期中成绩不能大于100")
        private BigDecimal midtermScore;

        @Schema(description = "期末成绩")
        @DecimalMin(value = "0", message = "期末成绩不能小于0")
        @DecimalMax(value = "100", message = "期末成绩不能大于100")
        private BigDecimal finalScore;

        @Schema(description = "实验成绩")
        @DecimalMin(value = "0", message = "实验成绩不能小于0")
        @DecimalMax(value = "100", message = "实验成绩不能大于100")
        private BigDecimal labScore;

        @Schema(description = "作业成绩")
        @DecimalMin(value = "0", message = "作业成绩不能小于0")
        @DecimalMax(value = "100", message = "作业成绩不能大于100")
        private BigDecimal assignmentScore;

        @Schema(description = "出勤成绩")
        @DecimalMin(value = "0", message = "出勤成绩不能小于0")
        @DecimalMax(value = "100", message = "出勤成绩不能大于100")
        private BigDecimal attendanceScore;

        @Schema(description = "备注")
        private String remark;
    }
}
