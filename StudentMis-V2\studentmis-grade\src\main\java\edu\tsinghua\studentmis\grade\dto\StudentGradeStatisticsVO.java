package edu.tsinghua.studentmis.grade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 学生成绩统计VO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Builder
@Schema(description = "学生成绩统计")
public class StudentGradeStatisticsVO {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学期ID")
    private Long semesterId;

    @Schema(description = "总课程数")
    private Integer totalCourses;

    @Schema(description = "通过课程数")
    private Integer passedCourses;

    @Schema(description = "不及格课程数")
    private Integer failedCourses;

    @Schema(description = "总学分")
    private BigDecimal totalCredits;

    @Schema(description = "获得学分")
    private BigDecimal earnedCredits;

    @Schema(description = "GPA")
    private BigDecimal gpa;

    @Schema(description = "加权平均分")
    private BigDecimal weightedScore;

    @Schema(description = "专业排名")
    private Integer rankInMajor;

    @Schema(description = "年级排名")
    private Integer rankInGrade;

    @Schema(description = "最高分")
    private BigDecimal highestScore;

    @Schema(description = "最低分")
    private BigDecimal lowestScore;

    @Schema(description = "平均分")
    private BigDecimal averageScore;

    @Schema(description = "优秀课程数(90分以上)")
    private Integer excellentCourses;

    @Schema(description = "良好课程数(80-89分)")
    private Integer goodCourses;

    @Schema(description = "中等课程数(70-79分)")
    private Integer averageCourses;

    @Schema(description = "及格课程数(60-69分)")
    private Integer passingCourses;

    @Schema(description = "成绩分布")
    private List<GradeDistributionItem> gradeDistribution;

    @Schema(description = "学期成绩趋势")
    private List<SemesterGradeTrend> gradeTrends;

    /**
     * 成绩分布项
     */
    @Data
    @Builder
    @Schema(description = "成绩分布项")
    public static class GradeDistributionItem {
        @Schema(description = "分数区间")
        private String scoreRange;
        
        @Schema(description = "课程数量")
        private Integer courseCount;
        
        @Schema(description = "占比")
        private BigDecimal percentage;
    }

    /**
     * 学期成绩趋势
     */
    @Data
    @Builder
    @Schema(description = "学期成绩趋势")
    public static class SemesterGradeTrend {
        @Schema(description = "学期名称")
        private String semesterName;
        
        @Schema(description = "GPA")
        private BigDecimal gpa;
        
        @Schema(description = "加权平均分")
        private BigDecimal weightedScore;
        
        @Schema(description = "通过率")
        private BigDecimal passRate;
    }

    /**
     * 计算通过率
     */
    public BigDecimal getPassRate() {
        if (totalCourses == null || totalCourses == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(passedCourses)
                .divide(new BigDecimal(totalCourses), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 计算学分获得率
     */
    public BigDecimal getCreditEarnRate() {
        if (totalCredits == null || totalCredits.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return earnedCredits.divide(totalCredits, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 获取GPA等级
     */
    public String getGpaLevel() {
        if (gpa == null) {
            return "未知";
        }
        
        if (gpa.compareTo(new BigDecimal("3.7")) >= 0) {
            return "优秀";
        } else if (gpa.compareTo(new BigDecimal("3.0")) >= 0) {
            return "良好";
        } else if (gpa.compareTo(new BigDecimal("2.0")) >= 0) {
            return "中等";
        } else if (gpa.compareTo(new BigDecimal("1.0")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }
}
