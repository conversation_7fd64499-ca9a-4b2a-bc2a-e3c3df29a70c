#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "   StudentMIS V2 系统启动脚本"
echo -e "   清华大学级学生成绩管理系统"
echo -e "========================================${NC}"
echo

# 检查操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac
echo -e "${BLUE}检测到操作系统: ${MACHINE}${NC}"

# 检查Java环境
echo -e "${YELLOW}[1/8] 检查Java环境...${NC}"
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    echo -e "${GREEN}✅ Java环境检查通过 (版本: ${JAVA_VERSION})${NC}"
else
    echo -e "${RED}❌ 错误: 未找到Java环境，请安装JDK 17或更高版本${NC}"
    exit 1
fi

# 检查Maven环境
echo -e "${YELLOW}[2/8] 检查Maven环境...${NC}"
if command -v mvn &> /dev/null; then
    MAVEN_VERSION=$(mvn -version 2>&1 | head -n 1 | awk '{print $3}')
    echo -e "${GREEN}✅ Maven环境检查通过 (版本: ${MAVEN_VERSION})${NC}"
else
    echo -e "${RED}❌ 错误: 未找到Maven环境，请安装Maven 3.8或更高版本${NC}"
    exit 1
fi

# 检查MySQL服务
echo -e "${YELLOW}[3/8] 检查MySQL服务...${NC}"
if command -v mysql &> /dev/null; then
    if pgrep -x "mysqld" > /dev/null; then
        echo -e "${GREEN}✅ MySQL服务运行中${NC}"
    else
        echo -e "${YELLOW}⚠️  警告: MySQL服务未运行，请启动MySQL服务${NC}"
        if [[ "$MACHINE" == "Mac" ]]; then
            echo "可以使用: brew services start mysql"
        elif [[ "$MACHINE" == "Linux" ]]; then
            echo "可以使用: sudo systemctl start mysql"
        fi
        read -p "按回车键继续..."
    fi
else
    echo -e "${RED}❌ 错误: 未找到MySQL，请安装MySQL 8.0或更高版本${NC}"
    exit 1
fi

# 检查Redis服务
echo -e "${YELLOW}[4/8] 检查Redis服务...${NC}"
if command -v redis-server &> /dev/null; then
    if pgrep -x "redis-server" > /dev/null; then
        echo -e "${GREEN}✅ Redis服务运行中${NC}"
    else
        echo -e "${YELLOW}⚠️  警告: Redis服务未运行，正在启动...${NC}"
        if [[ "$MACHINE" == "Mac" ]]; then
            brew services start redis &
        elif [[ "$MACHINE" == "Linux" ]]; then
            sudo systemctl start redis &
        else
            redis-server &
        fi
        sleep 3
    fi
else
    echo -e "${RED}❌ 错误: 未找到Redis，请安装Redis 7.0或更高版本${NC}"
    exit 1
fi

# 启动Nacos
echo -e "${YELLOW}[5/8] 启动Nacos服务注册中心...${NC}"
if [ ! -f "nacos/bin/startup.sh" ]; then
    echo -e "${RED}❌ 错误: 未找到Nacos，请下载并解压Nacos到当前目录${NC}"
    echo "下载地址: https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.tar.gz"
    exit 1
fi

cd nacos/bin
nohup ./startup.sh -m standalone > nacos.log 2>&1 &
cd ../..
echo -e "${GREEN}✅ Nacos启动中... (请等待30秒)${NC}"
sleep 30

# 编译项目
echo -e "${YELLOW}[6/8] 编译项目...${NC}"
echo "正在编译，请稍候..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 错误: 项目编译失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 项目编译完成${NC}"

# 启动微服务
echo -e "${YELLOW}[7/8] 启动微服务...${NC}"

echo "启动API网关..."
cd studentmis-gateway
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > gateway.log 2>&1 &
cd ..
sleep 10

echo "启动认证服务..."
cd studentmis-auth
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > auth.log 2>&1 &
cd ..
sleep 10

echo "启动学生服务..."
cd studentmis-student
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > student.log 2>&1 &
cd ..
sleep 10

echo "启动成绩服务..."
cd studentmis-grade
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > grade.log 2>&1 &
cd ..
sleep 10

echo "启动数据分析服务..."
cd studentmis-analytics
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > analytics.log 2>&1 &
cd ..

echo -e "${GREEN}✅ 所有微服务启动中...${NC}"

# 启动前端
echo -e "${YELLOW}[8/8] 启动前端应用...${NC}"
if [ -d "../StudentMis-V2-Frontend" ]; then
    cd ../StudentMis-V2-Frontend
    
    # 检查Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node -v)
        echo -e "${GREEN}✅ Node.js环境检查通过 (版本: ${NODE_VERSION})${NC}"
    else
        echo -e "${RED}❌ 错误: 未找到Node.js环境，请安装Node.js 18或更高版本${NC}"
        exit 1
    fi
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "正在安装前端依赖..."
        npm install
    fi
    
    echo "启动前端开发服务器..."
    nohup npm run dev > frontend.log 2>&1 &
    cd ../StudentMis-V2
else
    echo -e "${YELLOW}⚠️  警告: 未找到前端项目目录${NC}"
fi

echo
echo -e "${BLUE}========================================"
echo -e "   🎉 StudentMIS V2 启动完成!"
echo -e "========================================${NC}"
echo
echo -e "${GREEN}📋 服务访问地址:${NC}"
echo -e "   前端应用:     http://localhost:3000"
echo -e "   API网关:      http://localhost:8080"
echo -e "   Nacos控制台:  http://localhost:8848/nacos"
echo -e "   API文档:      http://localhost:8080/doc.html"
echo
echo -e "${GREEN}🔑 默认账号:${NC}"
echo -e "   管理员: admin / 123456"
echo -e "   学生:   2024001001 / 123456"
echo -e "   教师:   T001 / 123456"
echo
echo -e "${YELLOW}📝 注意事项:${NC}"
echo -e "   1. 请确保MySQL和Redis服务正常运行"
echo -e "   2. 首次启动可能需要等待1-2分钟"
echo -e "   3. 如遇问题请查看各服务的日志文件"
echo
echo -e "${BLUE}📊 服务状态检查:${NC}"
echo "等待服务启动完成..."
sleep 30

# 检查服务状态
check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✅ ${service_name} (端口:${port}) - 运行正常${NC}"
    else
        echo -e "${RED}❌ ${service_name} (端口:${port}) - 启动失败${NC}"
    fi
}

check_service "Nacos" "8848" "http://localhost:8848/nacos"
check_service "API网关" "8080" "http://localhost:8080/actuator/health"
check_service "前端应用" "3000" "http://localhost:3000"

echo
echo -e "${GREEN}系统启动完成！访问 http://localhost:3000 开始使用${NC}"

# 在macOS上自动打开浏览器
if [[ "$MACHINE" == "Mac" ]]; then
    open http://localhost:3000
elif [[ "$MACHINE" == "Linux" ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:3000
    fi
fi
