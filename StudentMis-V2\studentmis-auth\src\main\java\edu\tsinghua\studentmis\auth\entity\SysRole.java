package edu.tsinghua.studentmis.auth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import edu.tsinghua.studentmis.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统角色实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role")
@Schema(description = "系统角色")
public class SysRole extends BaseEntity {

    @Schema(description = "角色编码")
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    @TableField("role_code")
    private String roleCode;

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    @TableField("role_name")
    private String roleName;

    @Schema(description = "角色描述")
    @TableField("description")
    private String description;

    @Schema(description = "数据权限范围")
    @TableField("data_scope")
    private DataScope dataScope;

    @Schema(description = "角色状态")
    @TableField("status")
    private RoleStatus status;

    @Schema(description = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 数据权限范围枚举
     */
    public enum DataScope {
        ALL("全部数据权限"),
        DEPT("本部门数据权限"),
        DEPT_AND_SUB("本部门及以下数据权限"),
        SELF("仅本人数据权限");

        private final String description;

        DataScope(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 角色状态枚举
     */
    public enum RoleStatus {
        ACTIVE("启用"),
        INACTIVE("禁用");

        private final String description;

        RoleStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查角色是否启用
     */
    public boolean isEnabled() {
        return RoleStatus.ACTIVE.equals(this.status);
    }
}
