@echo off
chcp 65001
echo ========================================
echo   StudentMIS V2 系统测试脚本
echo   全面性系统性测试
echo ========================================
echo.

color 0B

:: 设置测试结果文件
set TEST_REPORT=test-report-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%.txt
echo 测试报告将保存到: %TEST_REPORT%
echo StudentMIS V2 系统测试报告 > %TEST_REPORT%
echo 测试时间: %date% %time% >> %TEST_REPORT%
echo ======================================== >> %TEST_REPORT%
echo. >> %TEST_REPORT%

:: 测试计数器
set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

:: 测试函数
:test_service
set /a TOTAL_TESTS+=1
echo [测试 %TOTAL_TESTS%] 测试 %~1...
curl -s --connect-timeout 10 --max-time 30 "%~2" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ %~1 - 测试通过
    echo [PASS] %~1 - %~2 >> %TEST_REPORT%
    set /a PASSED_TESTS+=1
) else (
    echo ❌ %~1 - 测试失败
    echo [FAIL] %~1 - %~2 >> %TEST_REPORT%
    set /a FAILED_TESTS+=1
)
echo.
goto :eof

:: 开始测试
echo 开始系统测试...
echo.

:: 1. 基础服务连通性测试
echo ========================================
echo 1. 基础服务连通性测试
echo ========================================

call :test_service "Nacos服务注册中心" "http://localhost:8848/nacos"
call :test_service "API网关健康检查" "http://localhost:8080/actuator/health"
call :test_service "前端应用" "http://localhost:3000"

:: 2. 微服务健康检查
echo ========================================
echo 2. 微服务健康检查
echo ========================================

call :test_service "认证服务健康检查" "http://localhost:8080/api/auth/actuator/health"
call :test_service "学生服务健康检查" "http://localhost:8080/api/student/actuator/health"
call :test_service "成绩服务健康检查" "http://localhost:8080/api/grade/actuator/health"
call :test_service "数据分析服务健康检查" "http://localhost:8080/api/analytics/actuator/health"

:: 3. API接口测试
echo ========================================
echo 3. API接口测试
echo ========================================

:: 测试登录接口
echo [测试 %TOTAL_TESTS%] 测试用户登录接口...
set /a TOTAL_TESTS+=1
curl -s -X POST -H "Content-Type: application/json" ^
     -d "{\"username\":\"admin\",\"password\":\"123456\"}" ^
     "http://localhost:8080/api/auth/login" > login_response.tmp 2>&1

if %errorlevel% equ 0 (
    findstr "token" login_response.tmp >nul
    if !errorlevel! equ 0 (
        echo ✅ 用户登录接口 - 测试通过
        echo [PASS] 用户登录接口 - 返回token >> %TEST_REPORT%
        set /a PASSED_TESTS+=1
        
        :: 提取token用于后续测试
        for /f "tokens=2 delims=:" %%a in ('findstr "token" login_response.tmp') do (
            set TOKEN=%%a
            set TOKEN=!TOKEN:"=!
            set TOKEN=!TOKEN:,=!
            set TOKEN=!TOKEN: =!
        )
    ) else (
        echo ❌ 用户登录接口 - 测试失败 (未返回token)
        echo [FAIL] 用户登录接口 - 未返回token >> %TEST_REPORT%
        set /a FAILED_TESTS+=1
    )
) else (
    echo ❌ 用户登录接口 - 测试失败 (请求失败)
    echo [FAIL] 用户登录接口 - 请求失败 >> %TEST_REPORT%
    set /a FAILED_TESTS+=1
)
del login_response.tmp 2>nul
echo.

:: 测试需要认证的接口
if defined TOKEN (
    echo [测试 %TOTAL_TESTS%] 测试获取用户信息接口...
    set /a TOTAL_TESTS+=1
    curl -s -H "Authorization: Bearer %TOKEN%" ^
         "http://localhost:8080/api/auth/me" > userinfo_response.tmp 2>&1
    
    if !errorlevel! equ 0 (
        findstr "userId" userinfo_response.tmp >nul
        if !errorlevel! equ 0 (
            echo ✅ 获取用户信息接口 - 测试通过
            echo [PASS] 获取用户信息接口 - 返回用户信息 >> %TEST_REPORT%
            set /a PASSED_TESTS+=1
        ) else (
            echo ❌ 获取用户信息接口 - 测试失败 (未返回用户信息)
            echo [FAIL] 获取用户信息接口 - 未返回用户信息 >> %TEST_REPORT%
            set /a FAILED_TESTS+=1
        )
    ) else (
        echo ❌ 获取用户信息接口 - 测试失败 (请求失败)
        echo [FAIL] 获取用户信息接口 - 请求失败 >> %TEST_REPORT%
        set /a FAILED_TESTS+=1
    )
    del userinfo_response.tmp 2>nul
    echo.
)

:: 4. 数据库连接测试
echo ========================================
echo 4. 数据库连接测试
echo ========================================

echo [测试 %TOTAL_TESTS%] 测试数据库连接...
set /a TOTAL_TESTS+=1
mysql -u studentmis -pStudentMIS@2024 -e "SELECT 1;" studentmis_v2 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 数据库连接 - 测试通过
    echo [PASS] 数据库连接 - MySQL连接正常 >> %TEST_REPORT%
    set /a PASSED_TESTS+=1
) else (
    echo ❌ 数据库连接 - 测试失败
    echo [FAIL] 数据库连接 - MySQL连接失败 >> %TEST_REPORT%
    set /a FAILED_TESTS+=1
)
echo.

:: 5. Redis连接测试
echo ========================================
echo 5. Redis连接测试
echo ========================================

echo [测试 %TOTAL_TESTS%] 测试Redis连接...
set /a TOTAL_TESTS+=1
redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis连接 - 测试通过
    echo [PASS] Redis连接 - Redis连接正常 >> %TEST_REPORT%
    set /a PASSED_TESTS+=1
) else (
    echo ❌ Redis连接 - 测试失败
    echo [FAIL] Redis连接 - Redis连接失败 >> %TEST_REPORT%
    set /a FAILED_TESTS+=1
)
echo.

:: 6. 前端资源加载测试
echo ========================================
echo 6. 前端资源加载测试
echo ========================================

call :test_service "前端主页面" "http://localhost:3000/"
call :test_service "前端静态资源" "http://localhost:3000/favicon.ico"

:: 7. API文档测试
echo ========================================
echo 7. API文档测试
echo ========================================

call :test_service "Swagger API文档" "http://localhost:8080/doc.html"

:: 8. 性能测试
echo ========================================
echo 8. 性能测试
echo ========================================

echo [测试 %TOTAL_TESTS%] 测试API响应时间...
set /a TOTAL_TESTS+=1
for /l %%i in (1,1,5) do (
    curl -s -w "%%{time_total}" -o nul "http://localhost:8080/actuator/health" >> response_times.tmp
    echo. >> response_times.tmp
)

if exist response_times.tmp (
    echo ✅ API响应时间测试 - 完成
    echo [PASS] API响应时间测试 - 5次请求完成 >> %TEST_REPORT%
    echo 响应时间详情: >> %TEST_REPORT%
    type response_times.tmp >> %TEST_REPORT%
    set /a PASSED_TESTS+=1
    del response_times.tmp
) else (
    echo ❌ API响应时间测试 - 失败
    echo [FAIL] API响应时间测试 - 无法获取响应时间 >> %TEST_REPORT%
    set /a FAILED_TESTS+=1
)
echo.

:: 9. 并发测试
echo ========================================
echo 9. 并发测试
echo ========================================

echo [测试 %TOTAL_TESTS%] 测试并发请求处理...
set /a TOTAL_TESTS+=1
for /l %%i in (1,1,10) do (
    start /b curl -s "http://localhost:8080/actuator/health" >nul 2>&1
)
timeout /t 5 /nobreak >nul
echo ✅ 并发请求测试 - 完成
echo [PASS] 并发请求测试 - 10个并发请求完成 >> %TEST_REPORT%
set /a PASSED_TESTS+=1
echo.

:: 10. 系统资源检查
echo ========================================
echo 10. 系统资源检查
echo ========================================

echo [测试 %TOTAL_TESTS%] 检查系统资源使用情况...
set /a TOTAL_TESTS+=1

:: 检查Java进程
tasklist /FI "IMAGENAME eq java.exe" /FO CSV | find /C "java.exe" > java_count.tmp
set /p JAVA_PROCESSES=<java_count.tmp
del java_count.tmp

if %JAVA_PROCESSES% geq 5 (
    echo ✅ Java进程检查 - 发现 %JAVA_PROCESSES% 个Java进程
    echo [PASS] Java进程检查 - %JAVA_PROCESSES% 个进程运行中 >> %TEST_REPORT%
    set /a PASSED_TESTS+=1
) else (
    echo ❌ Java进程检查 - 只发现 %JAVA_PROCESSES% 个Java进程
    echo [FAIL] Java进程检查 - 进程数量不足 >> %TEST_REPORT%
    set /a FAILED_TESTS+=1
)
echo.

:: 生成测试报告
echo ========================================
echo 测试完成 - 生成报告
echo ========================================

echo. >> %TEST_REPORT%
echo ======================================== >> %TEST_REPORT%
echo 测试总结 >> %TEST_REPORT%
echo ======================================== >> %TEST_REPORT%
echo 总测试数: %TOTAL_TESTS% >> %TEST_REPORT%
echo 通过测试: %PASSED_TESTS% >> %TEST_REPORT%
echo 失败测试: %FAILED_TESTS% >> %TEST_REPORT%

set /a SUCCESS_RATE=%PASSED_TESTS%*100/%TOTAL_TESTS%
echo 成功率: %SUCCESS_RATE%%% >> %TEST_REPORT%

echo.
echo ========================================
echo   🎯 测试结果汇总
echo ========================================
echo 总测试数: %TOTAL_TESTS%
echo 通过测试: %PASSED_TESTS%
echo 失败测试: %FAILED_TESTS%
echo 成功率: %SUCCESS_RATE%%%
echo.

if %FAILED_TESTS% equ 0 (
    echo ✅ 所有测试通过！系统运行正常
    echo [RESULT] 所有测试通过 - 系统状态良好 >> %TEST_REPORT%
) else (
    echo ❌ 发现 %FAILED_TESTS% 个测试失败，请检查系统状态
    echo [RESULT] 发现测试失败 - 需要检查系统 >> %TEST_REPORT%
)

echo.
echo 详细测试报告已保存到: %TEST_REPORT%
echo.

:: 询问是否打开测试报告
set /p OPEN_REPORT=是否打开测试报告？(Y/N): 
if /i "%OPEN_REPORT%"=="Y" (
    start notepad %TEST_REPORT%
)

echo 测试完成！
pause
