2025/06/19-19:27:56.080464 4be8 RocksDB version: 7.7.3
2025/06/19-19:27:56.080583 4be8 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-19:27:56.080595 4be8 Compile date 2022-10-24 17:17:55
2025/06/19-19:27:56.080609 4be8 DB SUMMARY
2025/06/19-19:27:56.080615 4be8 DB Session ID:  DEIGZIB5FYX57QXAYUQC
2025/06/19-19:27:56.081059 4be8 CURRENT file:  CURRENT
2025/06/19-19:27:56.081069 4be8 IDENTITY file:  IDENTITY
2025/06/19-19:27:56.081144 4be8 MANIFEST file:  MANIFEST-000005 size: 119 Bytes
2025/06/19-19:27:56.081156 4be8 SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log dir, Total Num: 0, files: 
2025/06/19-19:27:56.081167 4be8 Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log: 000004.log size: 110 ; 
2025/06/19-19:27:56.081257 4be8                         Options.error_if_exists: 0
2025/06/19-19:27:56.081262 4be8                       Options.create_if_missing: 1
2025/06/19-19:27:56.081264 4be8                         Options.paranoid_checks: 1
2025/06/19-19:27:56.081266 4be8             Options.flush_verify_memtable_count: 1
2025/06/19-19:27:56.081268 4be8                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-19:27:56.081270 4be8        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-19:27:56.081272 4be8                                     Options.env: 00000282D34347A0
2025/06/19-19:27:56.081274 4be8                                      Options.fs: WinFS
2025/06/19-19:27:56.081277 4be8                                Options.info_log: 00000282D01224B0
2025/06/19-19:27:56.081279 4be8                Options.max_file_opening_threads: 16
2025/06/19-19:27:56.081281 4be8                              Options.statistics: 00000282FE5C5B40
2025/06/19-19:27:56.081283 4be8                               Options.use_fsync: 0
2025/06/19-19:27:56.081285 4be8                       Options.max_log_file_size: 0
2025/06/19-19:27:56.081287 4be8                  Options.max_manifest_file_size: 1073741824
2025/06/19-19:27:56.081289 4be8                   Options.log_file_time_to_roll: 0
2025/06/19-19:27:56.081291 4be8                       Options.keep_log_file_num: 100
2025/06/19-19:27:56.081293 4be8                    Options.recycle_log_file_num: 0
2025/06/19-19:27:56.081295 4be8                         Options.allow_fallocate: 1
2025/06/19-19:27:56.081297 4be8                        Options.allow_mmap_reads: 0
2025/06/19-19:27:56.081299 4be8                       Options.allow_mmap_writes: 0
2025/06/19-19:27:56.081301 4be8                        Options.use_direct_reads: 0
2025/06/19-19:27:56.081303 4be8                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-19:27:56.081305 4be8          Options.create_missing_column_families: 1
2025/06/19-19:27:56.081307 4be8                              Options.db_log_dir: 
2025/06/19-19:27:56.081309 4be8                                 Options.wal_dir: 
2025/06/19-19:27:56.081311 4be8                Options.table_cache_numshardbits: 6
2025/06/19-19:27:56.081313 4be8                         Options.WAL_ttl_seconds: 0
2025/06/19-19:27:56.081316 4be8                       Options.WAL_size_limit_MB: 0
2025/06/19-19:27:56.081318 4be8                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-19:27:56.081320 4be8             Options.manifest_preallocation_size: 4194304
2025/06/19-19:27:56.081322 4be8                     Options.is_fd_close_on_exec: 1
2025/06/19-19:27:56.081324 4be8                   Options.advise_random_on_open: 1
2025/06/19-19:27:56.081326 4be8                    Options.db_write_buffer_size: 0
2025/06/19-19:27:56.081328 4be8                    Options.write_buffer_manager: 00000282D34363C0
2025/06/19-19:27:56.081330 4be8         Options.access_hint_on_compaction_start: 1
2025/06/19-19:27:56.081332 4be8           Options.random_access_max_buffer_size: 1048576
2025/06/19-19:27:56.081334 4be8                      Options.use_adaptive_mutex: 0
2025/06/19-19:27:56.081355 4be8                            Options.rate_limiter: 0000000000000000
2025/06/19-19:27:56.081359 4be8     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-19:27:56.081361 4be8                       Options.wal_recovery_mode: 2
2025/06/19-19:27:56.081363 4be8                  Options.enable_thread_tracking: 0
2025/06/19-19:27:56.081365 4be8                  Options.enable_pipelined_write: 0
2025/06/19-19:27:56.081367 4be8                  Options.unordered_write: 0
2025/06/19-19:27:56.081369 4be8         Options.allow_concurrent_memtable_write: 1
2025/06/19-19:27:56.081371 4be8      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-19:27:56.081373 4be8             Options.write_thread_max_yield_usec: 100
2025/06/19-19:27:56.081375 4be8            Options.write_thread_slow_yield_usec: 3
2025/06/19-19:27:56.081377 4be8                               Options.row_cache: None
2025/06/19-19:27:56.081379 4be8                              Options.wal_filter: None
2025/06/19-19:27:56.081381 4be8             Options.avoid_flush_during_recovery: 0
2025/06/19-19:27:56.081383 4be8             Options.allow_ingest_behind: 0
2025/06/19-19:27:56.081385 4be8             Options.two_write_queues: 0
2025/06/19-19:27:56.081387 4be8             Options.manual_wal_flush: 0
2025/06/19-19:27:56.081389 4be8             Options.wal_compression: 0
2025/06/19-19:27:56.081391 4be8             Options.atomic_flush: 0
2025/06/19-19:27:56.081393 4be8             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-19:27:56.081395 4be8                 Options.persist_stats_to_disk: 0
2025/06/19-19:27:56.081397 4be8                 Options.write_dbid_to_manifest: 0
2025/06/19-19:27:56.081399 4be8                 Options.log_readahead_size: 0
2025/06/19-19:27:56.081401 4be8                 Options.file_checksum_gen_factory: Unknown
2025/06/19-19:27:56.081403 4be8                 Options.best_efforts_recovery: 0
2025/06/19-19:27:56.081405 4be8                Options.max_bgerror_resume_count: 2147483647
2025/06/19-19:27:56.081407 4be8            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-19:27:56.081409 4be8             Options.allow_data_in_errors: 0
2025/06/19-19:27:56.081411 4be8             Options.db_host_id: __hostname__
2025/06/19-19:27:56.081413 4be8             Options.enforce_single_del_contracts: true
2025/06/19-19:27:56.081415 4be8             Options.max_background_jobs: 2
2025/06/19-19:27:56.081417 4be8             Options.max_background_compactions: 4
2025/06/19-19:27:56.081419 4be8             Options.max_subcompactions: 1
2025/06/19-19:27:56.081421 4be8             Options.avoid_flush_during_shutdown: 0
2025/06/19-19:27:56.081423 4be8           Options.writable_file_max_buffer_size: 1048576
2025/06/19-19:27:56.081425 4be8             Options.delayed_write_rate : 16777216
2025/06/19-19:27:56.081428 4be8             Options.max_total_wal_size: 1073741824
2025/06/19-19:27:56.081430 4be8             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-19:27:56.081432 4be8                   Options.stats_dump_period_sec: 600
2025/06/19-19:27:56.081434 4be8                 Options.stats_persist_period_sec: 600
2025/06/19-19:27:56.081436 4be8                 Options.stats_history_buffer_size: 1048576
2025/06/19-19:27:56.081438 4be8                          Options.max_open_files: -1
2025/06/19-19:27:56.081440 4be8                          Options.bytes_per_sync: 0
2025/06/19-19:27:56.081442 4be8                      Options.wal_bytes_per_sync: 0
2025/06/19-19:27:56.081444 4be8                   Options.strict_bytes_per_sync: 0
2025/06/19-19:27:56.081446 4be8       Options.compaction_readahead_size: 0
2025/06/19-19:27:56.081448 4be8                  Options.max_background_flushes: 1
2025/06/19-19:27:56.081451 4be8 Compression algorithms supported:
2025/06/19-19:27:56.081457 4be8 	kZSTD supported: 1
2025/06/19-19:27:56.081460 4be8 	kSnappyCompression supported: 1
2025/06/19-19:27:56.081462 4be8 	kBZip2Compression supported: 0
2025/06/19-19:27:56.081464 4be8 	kZlibCompression supported: 1
2025/06/19-19:27:56.081486 4be8 	kLZ4Compression supported: 1
2025/06/19-19:27:56.081489 4be8 	kXpressCompression supported: 0
2025/06/19-19:27:56.081491 4be8 	kLZ4HCCompression supported: 1
2025/06/19-19:27:56.081493 4be8 	kZSTDNotFinalCompression supported: 1
2025/06/19-19:27:56.081499 4be8 Fast CRC32 supported: Not supported on x86
2025/06/19-19:27:56.081502 4be8 DMutex implementation: std::mutex
2025/06/19-19:27:56.082353 4be8 [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000005
2025/06/19-19:27:56.082620 4be8 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-19:27:56.082628 4be8               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:27:56.082631 4be8           Options.merge_operator: StringAppendOperator
2025/06/19-19:27:56.082633 4be8        Options.compaction_filter: None
2025/06/19-19:27:56.082635 4be8        Options.compaction_filter_factory: None
2025/06/19-19:27:56.082637 4be8  Options.sst_partitioner_factory: None
2025/06/19-19:27:56.082639 4be8         Options.memtable_factory: SkipListFactory
2025/06/19-19:27:56.082641 4be8            Options.table_factory: BlockBasedTable
2025/06/19-19:27:56.082667 4be8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000282D2BFCD10)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 00000282D4010C60
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:27:56.082670 4be8        Options.write_buffer_size: 67108864
2025/06/19-19:27:56.082672 4be8  Options.max_write_buffer_number: 3
2025/06/19-19:27:56.082674 4be8          Options.compression: Snappy
2025/06/19-19:27:56.082676 4be8                  Options.bottommost_compression: Disabled
2025/06/19-19:27:56.082679 4be8       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:27:56.082681 4be8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:27:56.082683 4be8             Options.num_levels: 7
2025/06/19-19:27:56.082685 4be8        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:27:56.082687 4be8     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:27:56.082689 4be8     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:27:56.082691 4be8            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:27:56.082693 4be8                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:27:56.082695 4be8               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:27:56.082697 4be8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.082699 4be8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.082702 4be8         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:27:56.082704 4be8                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:27:56.082728 4be8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.082731 4be8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.082733 4be8            Options.compression_opts.window_bits: -14
2025/06/19-19:27:56.082735 4be8                  Options.compression_opts.level: 32767
2025/06/19-19:27:56.082737 4be8               Options.compression_opts.strategy: 0
2025/06/19-19:27:56.082739 4be8         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.082741 4be8         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.082743 4be8         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.082745 4be8         Options.compression_opts.parallel_threads: 1
2025/06/19-19:27:56.082747 4be8                  Options.compression_opts.enabled: false
2025/06/19-19:27:56.082749 4be8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.082751 4be8      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:27:56.082753 4be8          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:27:56.082755 4be8              Options.level0_stop_writes_trigger: 40
2025/06/19-19:27:56.082757 4be8                   Options.target_file_size_base: 67108864
2025/06/19-19:27:56.082759 4be8             Options.target_file_size_multiplier: 1
2025/06/19-19:27:56.082761 4be8                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:27:56.082763 4be8 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:27:56.082765 4be8          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:27:56.082772 4be8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:27:56.082774 4be8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:27:56.082776 4be8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:27:56.082778 4be8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:27:56.082780 4be8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:27:56.082782 4be8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:27:56.082784 4be8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:27:56.082786 4be8       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:27:56.082788 4be8                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:27:56.082790 4be8                        Options.arena_block_size: 1048576
2025/06/19-19:27:56.082792 4be8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:27:56.082794 4be8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:27:56.082796 4be8                Options.disable_auto_compactions: 0
2025/06/19-19:27:56.082799 4be8                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:27:56.082802 4be8                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:27:56.082804 4be8 Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:27:56.082806 4be8 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:27:56.082808 4be8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:27:56.082810 4be8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:27:56.082813 4be8 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:27:56.082815 4be8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:27:56.082817 4be8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:27:56.082819 4be8 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:27:56.082823 4be8                   Options.table_properties_collectors: 
2025/06/19-19:27:56.082825 4be8                   Options.inplace_update_support: 0
2025/06/19-19:27:56.082827 4be8                 Options.inplace_update_num_locks: 10000
2025/06/19-19:27:56.082829 4be8               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:27:56.082832 4be8               Options.memtable_whole_key_filtering: 0
2025/06/19-19:27:56.082834 4be8   Options.memtable_huge_page_size: 0
2025/06/19-19:27:56.082836 4be8                           Options.bloom_locality: 0
2025/06/19-19:27:56.082838 4be8                    Options.max_successive_merges: 0
2025/06/19-19:27:56.082840 4be8                Options.optimize_filters_for_hits: 0
2025/06/19-19:27:56.082842 4be8                Options.paranoid_file_checks: 0
2025/06/19-19:27:56.082844 4be8                Options.force_consistency_checks: 1
2025/06/19-19:27:56.082846 4be8                Options.report_bg_io_stats: 0
2025/06/19-19:27:56.082848 4be8                               Options.ttl: 2592000
2025/06/19-19:27:56.082850 4be8          Options.periodic_compaction_seconds: 0
2025/06/19-19:27:56.082852 4be8  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:27:56.082854 4be8                       Options.enable_blob_files: false
2025/06/19-19:27:56.082857 4be8                           Options.min_blob_size: 0
2025/06/19-19:27:56.082859 4be8                          Options.blob_file_size: 268435456
2025/06/19-19:27:56.082861 4be8                   Options.blob_compression_type: NoCompression
2025/06/19-19:27:56.082863 4be8          Options.enable_blob_garbage_collection: false
2025/06/19-19:27:56.082865 4be8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:27:56.082867 4be8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:27:56.082869 4be8          Options.blob_compaction_readahead_size: 0
2025/06/19-19:27:56.082871 4be8                Options.blob_file_starting_level: 0
2025/06/19-19:27:56.082873 4be8 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:27:56.084214 4be8 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-19:27:56.084220 4be8               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:27:56.084222 4be8           Options.merge_operator: StringAppendOperator
2025/06/19-19:27:56.084224 4be8        Options.compaction_filter: None
2025/06/19-19:27:56.084226 4be8        Options.compaction_filter_factory: None
2025/06/19-19:27:56.084228 4be8  Options.sst_partitioner_factory: None
2025/06/19-19:27:56.084231 4be8         Options.memtable_factory: SkipListFactory
2025/06/19-19:27:56.084233 4be8            Options.table_factory: BlockBasedTable
2025/06/19-19:27:56.084257 4be8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000282D2BFCD10)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 00000282D4010C60
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:27:56.084260 4be8        Options.write_buffer_size: 67108864
2025/06/19-19:27:56.084262 4be8  Options.max_write_buffer_number: 3
2025/06/19-19:27:56.084264 4be8          Options.compression: Snappy
2025/06/19-19:27:56.084266 4be8                  Options.bottommost_compression: Disabled
2025/06/19-19:27:56.084268 4be8       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:27:56.084272 4be8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:27:56.084275 4be8             Options.num_levels: 7
2025/06/19-19:27:56.084276 4be8        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:27:56.084279 4be8     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:27:56.084281 4be8     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:27:56.084283 4be8            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:27:56.084285 4be8                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:27:56.084287 4be8               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:27:56.084289 4be8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.084291 4be8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.084293 4be8         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:27:56.084295 4be8                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:27:56.084298 4be8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.084300 4be8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.084302 4be8            Options.compression_opts.window_bits: -14
2025/06/19-19:27:56.084304 4be8                  Options.compression_opts.level: 32767
2025/06/19-19:27:56.084306 4be8               Options.compression_opts.strategy: 0
2025/06/19-19:27:56.084308 4be8         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.084310 4be8         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.084312 4be8         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.084314 4be8         Options.compression_opts.parallel_threads: 1
2025/06/19-19:27:56.084316 4be8                  Options.compression_opts.enabled: false
2025/06/19-19:27:56.084318 4be8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.084320 4be8      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:27:56.084322 4be8          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:27:56.084324 4be8              Options.level0_stop_writes_trigger: 40
2025/06/19-19:27:56.084327 4be8                   Options.target_file_size_base: 67108864
2025/06/19-19:27:56.084329 4be8             Options.target_file_size_multiplier: 1
2025/06/19-19:27:56.084331 4be8                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:27:56.084333 4be8 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:27:56.084335 4be8          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:27:56.084338 4be8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:27:56.084340 4be8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:27:56.084342 4be8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:27:56.084344 4be8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:27:56.084346 4be8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:27:56.084348 4be8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:27:56.084350 4be8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:27:56.084352 4be8       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:27:56.084354 4be8                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:27:56.084356 4be8                        Options.arena_block_size: 1048576
2025/06/19-19:27:56.084358 4be8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:27:56.084361 4be8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:27:56.084363 4be8                Options.disable_auto_compactions: 0
2025/06/19-19:27:56.084366 4be8                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:27:56.084368 4be8                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:27:56.084371 4be8 Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:27:56.084374 4be8 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:27:56.084376 4be8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:27:56.084378 4be8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:27:56.084380 4be8 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:27:56.084383 4be8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:27:56.084385 4be8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:27:56.084387 4be8 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:27:56.084391 4be8                   Options.table_properties_collectors: 
2025/06/19-19:27:56.084393 4be8                   Options.inplace_update_support: 0
2025/06/19-19:27:56.084395 4be8                 Options.inplace_update_num_locks: 10000
2025/06/19-19:27:56.084397 4be8               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:27:56.084399 4be8               Options.memtable_whole_key_filtering: 0
2025/06/19-19:27:56.084401 4be8   Options.memtable_huge_page_size: 0
2025/06/19-19:27:56.084403 4be8                           Options.bloom_locality: 0
2025/06/19-19:27:56.084405 4be8                    Options.max_successive_merges: 0
2025/06/19-19:27:56.084408 4be8                Options.optimize_filters_for_hits: 0
2025/06/19-19:27:56.084410 4be8                Options.paranoid_file_checks: 0
2025/06/19-19:27:56.084412 4be8                Options.force_consistency_checks: 1
2025/06/19-19:27:56.084414 4be8                Options.report_bg_io_stats: 0
2025/06/19-19:27:56.084416 4be8                               Options.ttl: 2592000
2025/06/19-19:27:56.084418 4be8          Options.periodic_compaction_seconds: 0
2025/06/19-19:27:56.084420 4be8  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:27:56.084422 4be8                       Options.enable_blob_files: false
2025/06/19-19:27:56.084424 4be8                           Options.min_blob_size: 0
2025/06/19-19:27:56.084426 4be8                          Options.blob_file_size: 268435456
2025/06/19-19:27:56.084428 4be8                   Options.blob_compression_type: NoCompression
2025/06/19-19:27:56.084432 4be8          Options.enable_blob_garbage_collection: false
2025/06/19-19:27:56.084436 4be8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:27:56.084438 4be8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:27:56.084441 4be8          Options.blob_compaction_readahead_size: 0
2025/06/19-19:27:56.084443 4be8                Options.blob_file_starting_level: 0
2025/06/19-19:27:56.084445 4be8 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:27:56.087455 4be8 [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000005 succeeded,manifest_file_number is 5, next_file_number is 7, last_sequence is 0, log_number is 4,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/06/19-19:27:56.087463 4be8 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 0
2025/06/19-19:27:56.087465 4be8 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 4
2025/06/19-19:27:56.087721 4be8 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9d-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-19:27:56.088653 4be8 EVENT_LOG_v1 {"time_micros": 1750332476088637, "job": 1, "event": "recovery_started", "wal_files": [4]}
2025/06/19-19:27:56.088662 4be8 [db\db_impl\db_impl_open.cc:1029] Recovering log #4 mode 2
2025/06/19-19:27:56.090373 4be8 EVENT_LOG_v1 {"time_micros": 1750332476090343, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 10, "file_size": 1117, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 1, "largest_seqno": 1, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750332476, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "DEIGZIB5FYX57QXAYUQC", "orig_file_number": 10, "seqno_to_time_mapping": "N/A"}}
2025/06/19-19:27:56.093235 4be8 EVENT_LOG_v1 {"time_micros": 1750332476093207, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 11, "file_size": 1123, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 2, "largest_seqno": 2, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750332476, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "DEIGZIB5FYX57QXAYUQC", "orig_file_number": 11, "seqno_to_time_mapping": "N/A"}}
2025/06/19-19:27:56.095519 4be8 EVENT_LOG_v1 {"time_micros": 1750332476095515, "job": 1, "event": "recovery_finished"}
2025/06/19-19:27:56.095864 4be8 [db\version_set.cc:5051] Creating manifest 13
2025/06/19-19:27:56.102952 4be8 [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/000004.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-19:27:56.102976 4be8 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 00000282D1B3D400
2025/06/19-19:27:56.103391 4be8 DB pointer 00000282D3D43480
2025/06/19-19:27:56.103857 3edc [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-19:27:56.103870 3edc [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.09 KB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.09 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@00000282D4010C60#9160 capacity: 512.00 MB usage: 0.43 KB table_size: 4096 occupancy: 3 collections: 1 last_copies: 1 last_secs: 0.000113 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(2,0.18 KB,3.42727e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.10 KB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      1/0    1.10 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.0      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@00000282D4010C60#9160 capacity: 512.00 MB usage: 0.43 KB table_size: 4096 occupancy: 3 collections: 1 last_copies: 1 last_secs: 0.000113 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(2,0.18 KB,3.42727e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 2 Average: 8.5000  StdDev: 2.50
Min: 6  Median: 6.0000  Max: 11
Percentiles: P50: 6.00 P75: 11.00 P99: 11.00 P99.9: 11.00 P99.99: 11.00
------------------------------------------------------
(       4,       6 ]        1  50.000%  50.000% ##########
(      10,      15 ]        1  50.000% 100.000% ##########


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 2 Average: 8.5000  StdDev: 2.50
Min: 6  Median: 6.0000  Max: 11
Percentiles: P50: 6.00 P75: 11.00 P99: 11.00 P99.9: 11.00 P99.99: 11.00
------------------------------------------------------
(       4,       6 ]        1  50.000%  50.000% ##########
(      10,      15 ]        1  50.000% 100.000% ##########

2025/06/19-19:27:56.104527 3edc [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 2
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 2
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 2
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 2
rocksdb.block.cache.index.bytes.insert COUNT : 184
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 0
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 0
rocksdb.block.cache.data.bytes.insert COUNT : 0
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 184
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 2
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2240
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 4480
rocksdb.non.last.level.read.count COUNT : 4
rocksdb.block.checksum.compute.count COUNT : 6
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 380.000000 P95 : 449.000000 P99 : 449.000000 P100 : 449.000000 COUNT : 2 SUM : 796
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 406.000000 P95 : 406.000000 P99 : 406.000000 P100 : 406.000000 COUNT : 1 SUM : 406
rocksdb.table.open.io.micros P50 : 76.000000 P95 : 196.000000 P99 : 196.000000 P100 : 196.000000 COUNT : 2 SUM : 254
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.666667 P95 : 2.800000 P99 : 2.960000 P100 : 3.000000 COUNT : 4 SUM : 5
rocksdb.write.raw.block.micros P50 : 0.555556 P95 : 17.000000 P99 : 17.000000 P100 : 17.000000 COUNT : 10 SUM : 24
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 6.000000 P95 : 11.000000 P99 : 11.000000 P100 : 11.000000 COUNT : 4 SUM : 34
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1032.000000 P95 : 1032.000000 P99 : 1032.000000 P100 : 1032.000000 COUNT : 2 SUM : 2058
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-19:27:57.104604 3edc [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-19:27:57.104630 3edc [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-19:27:57.104635 3edc [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
