2025/06/19-19:26:11.596792 46bc RocksDB version: 7.7.3
2025/06/19-19:26:11.596861 46bc Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-19:26:11.596879 46bc Compile date 2022-10-24 17:17:55
2025/06/19-19:26:11.596893 46bc DB SUMMARY
2025/06/19-19:26:11.596899 46bc DB Session ID:  K4XFAHQXE6EOWWNXS3EU
2025/06/19-19:26:11.597147 46bc SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log dir, Total Num: 0, files: 
2025/06/19-19:26:11.597162 46bc Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log: 
2025/06/19-19:26:11.597174 46bc                         Options.error_if_exists: 0
2025/06/19-19:26:11.597304 46bc                       Options.create_if_missing: 1
2025/06/19-19:26:11.597309 46bc                         Options.paranoid_checks: 1
2025/06/19-19:26:11.597311 46bc             Options.flush_verify_memtable_count: 1
2025/06/19-19:26:11.597313 46bc                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-19:26:11.597315 46bc        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-19:26:11.597317 46bc                                     Options.env: 000001366FB452F0
2025/06/19-19:26:11.597320 46bc                                      Options.fs: WinFS
2025/06/19-19:26:11.597322 46bc                                Options.info_log: 00000136710218B0
2025/06/19-19:26:11.597325 46bc                Options.max_file_opening_threads: 16
2025/06/19-19:26:11.597327 46bc                              Options.statistics: 000001366FD90AF0
2025/06/19-19:26:11.597329 46bc                               Options.use_fsync: 0
2025/06/19-19:26:11.597331 46bc                       Options.max_log_file_size: 0
2025/06/19-19:26:11.597334 46bc                  Options.max_manifest_file_size: 1073741824
2025/06/19-19:26:11.597336 46bc                   Options.log_file_time_to_roll: 0
2025/06/19-19:26:11.597338 46bc                       Options.keep_log_file_num: 100
2025/06/19-19:26:11.597340 46bc                    Options.recycle_log_file_num: 0
2025/06/19-19:26:11.597342 46bc                         Options.allow_fallocate: 1
2025/06/19-19:26:11.597344 46bc                        Options.allow_mmap_reads: 0
2025/06/19-19:26:11.597347 46bc                       Options.allow_mmap_writes: 0
2025/06/19-19:26:11.597349 46bc                        Options.use_direct_reads: 0
2025/06/19-19:26:11.597351 46bc                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-19:26:11.597353 46bc          Options.create_missing_column_families: 1
2025/06/19-19:26:11.597355 46bc                              Options.db_log_dir: 
2025/06/19-19:26:11.597357 46bc                                 Options.wal_dir: 
2025/06/19-19:26:11.597359 46bc                Options.table_cache_numshardbits: 6
2025/06/19-19:26:11.597362 46bc                         Options.WAL_ttl_seconds: 0
2025/06/19-19:26:11.597364 46bc                       Options.WAL_size_limit_MB: 0
2025/06/19-19:26:11.597366 46bc                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-19:26:11.597368 46bc             Options.manifest_preallocation_size: 4194304
2025/06/19-19:26:11.597370 46bc                     Options.is_fd_close_on_exec: 1
2025/06/19-19:26:11.597372 46bc                   Options.advise_random_on_open: 1
2025/06/19-19:26:11.597375 46bc                    Options.db_write_buffer_size: 0
2025/06/19-19:26:11.597377 46bc                    Options.write_buffer_manager: 000001366FB453E0
2025/06/19-19:26:11.597379 46bc         Options.access_hint_on_compaction_start: 1
2025/06/19-19:26:11.597381 46bc           Options.random_access_max_buffer_size: 1048576
2025/06/19-19:26:11.597383 46bc                      Options.use_adaptive_mutex: 0
2025/06/19-19:26:11.597385 46bc                            Options.rate_limiter: 0000000000000000
2025/06/19-19:26:11.597388 46bc     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-19:26:11.597390 46bc                       Options.wal_recovery_mode: 2
2025/06/19-19:26:11.597409 46bc                  Options.enable_thread_tracking: 0
2025/06/19-19:26:11.597412 46bc                  Options.enable_pipelined_write: 0
2025/06/19-19:26:11.597415 46bc                  Options.unordered_write: 0
2025/06/19-19:26:11.597417 46bc         Options.allow_concurrent_memtable_write: 1
2025/06/19-19:26:11.597419 46bc      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-19:26:11.597421 46bc             Options.write_thread_max_yield_usec: 100
2025/06/19-19:26:11.597423 46bc            Options.write_thread_slow_yield_usec: 3
2025/06/19-19:26:11.597425 46bc                               Options.row_cache: None
2025/06/19-19:26:11.597427 46bc                              Options.wal_filter: None
2025/06/19-19:26:11.597430 46bc             Options.avoid_flush_during_recovery: 0
2025/06/19-19:26:11.597432 46bc             Options.allow_ingest_behind: 0
2025/06/19-19:26:11.597434 46bc             Options.two_write_queues: 0
2025/06/19-19:26:11.597436 46bc             Options.manual_wal_flush: 0
2025/06/19-19:26:11.597438 46bc             Options.wal_compression: 0
2025/06/19-19:26:11.597440 46bc             Options.atomic_flush: 0
2025/06/19-19:26:11.597442 46bc             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-19:26:11.597444 46bc                 Options.persist_stats_to_disk: 0
2025/06/19-19:26:11.597446 46bc                 Options.write_dbid_to_manifest: 0
2025/06/19-19:26:11.597448 46bc                 Options.log_readahead_size: 0
2025/06/19-19:26:11.597451 46bc                 Options.file_checksum_gen_factory: Unknown
2025/06/19-19:26:11.597453 46bc                 Options.best_efforts_recovery: 0
2025/06/19-19:26:11.597455 46bc                Options.max_bgerror_resume_count: 2147483647
2025/06/19-19:26:11.597457 46bc            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-19:26:11.597459 46bc             Options.allow_data_in_errors: 0
2025/06/19-19:26:11.597462 46bc             Options.db_host_id: __hostname__
2025/06/19-19:26:11.597464 46bc             Options.enforce_single_del_contracts: true
2025/06/19-19:26:11.597466 46bc             Options.max_background_jobs: 2
2025/06/19-19:26:11.597468 46bc             Options.max_background_compactions: 4
2025/06/19-19:26:11.597470 46bc             Options.max_subcompactions: 1
2025/06/19-19:26:11.597472 46bc             Options.avoid_flush_during_shutdown: 0
2025/06/19-19:26:11.597475 46bc           Options.writable_file_max_buffer_size: 1048576
2025/06/19-19:26:11.597477 46bc             Options.delayed_write_rate : 16777216
2025/06/19-19:26:11.597479 46bc             Options.max_total_wal_size: 1073741824
2025/06/19-19:26:11.597481 46bc             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-19:26:11.597483 46bc                   Options.stats_dump_period_sec: 600
2025/06/19-19:26:11.597486 46bc                 Options.stats_persist_period_sec: 600
2025/06/19-19:26:11.597488 46bc                 Options.stats_history_buffer_size: 1048576
2025/06/19-19:26:11.597490 46bc                          Options.max_open_files: -1
2025/06/19-19:26:11.597492 46bc                          Options.bytes_per_sync: 0
2025/06/19-19:26:11.597494 46bc                      Options.wal_bytes_per_sync: 0
2025/06/19-19:26:11.597497 46bc                   Options.strict_bytes_per_sync: 0
2025/06/19-19:26:11.597499 46bc       Options.compaction_readahead_size: 0
2025/06/19-19:26:11.597501 46bc                  Options.max_background_flushes: 1
2025/06/19-19:26:11.597503 46bc Compression algorithms supported:
2025/06/19-19:26:11.597512 46bc 	kZSTD supported: 1
2025/06/19-19:26:11.597515 46bc 	kSnappyCompression supported: 1
2025/06/19-19:26:11.597517 46bc 	kBZip2Compression supported: 0
2025/06/19-19:26:11.597520 46bc 	kZlibCompression supported: 1
2025/06/19-19:26:11.597522 46bc 	kLZ4Compression supported: 1
2025/06/19-19:26:11.597524 46bc 	kXpressCompression supported: 0
2025/06/19-19:26:11.597527 46bc 	kLZ4HCCompression supported: 1
2025/06/19-19:26:11.597547 46bc 	kZSTDNotFinalCompression supported: 1
2025/06/19-19:26:11.597561 46bc Fast CRC32 supported: Not supported on x86
2025/06/19-19:26:11.597563 46bc DMutex implementation: std::mutex
2025/06/19-19:26:11.600229 46bc [db\db_impl\db_impl_open.cc:313] Creating manifest 1 
2025/06/19-19:26:11.603470 46bc [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000001
2025/06/19-19:26:11.603740 46bc [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-19:26:11.603751 46bc               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:26:11.603754 46bc           Options.merge_operator: StringAppendOperator
2025/06/19-19:26:11.603757 46bc        Options.compaction_filter: None
2025/06/19-19:26:11.603759 46bc        Options.compaction_filter_factory: None
2025/06/19-19:26:11.603761 46bc  Options.sst_partitioner_factory: None
2025/06/19-19:26:11.603763 46bc         Options.memtable_factory: SkipListFactory
2025/06/19-19:26:11.603766 46bc            Options.table_factory: BlockBasedTable
2025/06/19-19:26:11.603793 46bc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001366C4B36B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001367064F6B0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:26:11.603797 46bc        Options.write_buffer_size: 67108864
2025/06/19-19:26:11.603799 46bc  Options.max_write_buffer_number: 3
2025/06/19-19:26:11.603802 46bc          Options.compression: Snappy
2025/06/19-19:26:11.603804 46bc                  Options.bottommost_compression: Disabled
2025/06/19-19:26:11.603806 46bc       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:26:11.603809 46bc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:26:11.603811 46bc             Options.num_levels: 7
2025/06/19-19:26:11.603813 46bc        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:26:11.603815 46bc     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:26:11.603817 46bc     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:26:11.603819 46bc            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:26:11.603822 46bc                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:26:11.603824 46bc               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:26:11.603826 46bc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.603828 46bc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.603831 46bc         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:26:11.603833 46bc                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:26:11.603835 46bc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.603837 46bc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.603842 46bc            Options.compression_opts.window_bits: -14
2025/06/19-19:26:11.603846 46bc                  Options.compression_opts.level: 32767
2025/06/19-19:26:11.603848 46bc               Options.compression_opts.strategy: 0
2025/06/19-19:26:11.603850 46bc         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.603852 46bc         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.603854 46bc         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.603856 46bc         Options.compression_opts.parallel_threads: 1
2025/06/19-19:26:11.603859 46bc                  Options.compression_opts.enabled: false
2025/06/19-19:26:11.603861 46bc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.603863 46bc      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:26:11.603865 46bc          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:26:11.603867 46bc              Options.level0_stop_writes_trigger: 40
2025/06/19-19:26:11.603869 46bc                   Options.target_file_size_base: 67108864
2025/06/19-19:26:11.603872 46bc             Options.target_file_size_multiplier: 1
2025/06/19-19:26:11.603874 46bc                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:26:11.603876 46bc Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:26:11.603878 46bc          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:26:11.603885 46bc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:26:11.603887 46bc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:26:11.603889 46bc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:26:11.603891 46bc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:26:11.603893 46bc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:26:11.603896 46bc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:26:11.603898 46bc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:26:11.603900 46bc       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:26:11.603902 46bc                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:26:11.603904 46bc                        Options.arena_block_size: 1048576
2025/06/19-19:26:11.603906 46bc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:26:11.603908 46bc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:26:11.603911 46bc                Options.disable_auto_compactions: 0
2025/06/19-19:26:11.603914 46bc                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:26:11.603916 46bc                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:26:11.603918 46bc Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:26:11.603921 46bc Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:26:11.603923 46bc Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:26:11.603925 46bc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:26:11.603927 46bc Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:26:11.603930 46bc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:26:11.603932 46bc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:26:11.603934 46bc Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:26:11.603938 46bc                   Options.table_properties_collectors: 
2025/06/19-19:26:11.603940 46bc                   Options.inplace_update_support: 0
2025/06/19-19:26:11.603942 46bc                 Options.inplace_update_num_locks: 10000
2025/06/19-19:26:11.603944 46bc               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:26:11.603947 46bc               Options.memtable_whole_key_filtering: 0
2025/06/19-19:26:11.603949 46bc   Options.memtable_huge_page_size: 0
2025/06/19-19:26:11.603986 46bc                           Options.bloom_locality: 0
2025/06/19-19:26:11.603989 46bc                    Options.max_successive_merges: 0
2025/06/19-19:26:11.603991 46bc                Options.optimize_filters_for_hits: 0
2025/06/19-19:26:11.603994 46bc                Options.paranoid_file_checks: 0
2025/06/19-19:26:11.603996 46bc                Options.force_consistency_checks: 1
2025/06/19-19:26:11.603998 46bc                Options.report_bg_io_stats: 0
2025/06/19-19:26:11.604000 46bc                               Options.ttl: 2592000
2025/06/19-19:26:11.604002 46bc          Options.periodic_compaction_seconds: 0
2025/06/19-19:26:11.604004 46bc  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:26:11.604006 46bc                       Options.enable_blob_files: false
2025/06/19-19:26:11.604008 46bc                           Options.min_blob_size: 0
2025/06/19-19:26:11.604011 46bc                          Options.blob_file_size: 268435456
2025/06/19-19:26:11.604013 46bc                   Options.blob_compression_type: NoCompression
2025/06/19-19:26:11.604015 46bc          Options.enable_blob_garbage_collection: false
2025/06/19-19:26:11.604018 46bc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:26:11.604020 46bc Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:26:11.604023 46bc          Options.blob_compaction_readahead_size: 0
2025/06/19-19:26:11.604025 46bc                Options.blob_file_starting_level: 0
2025/06/19-19:26:11.604027 46bc Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:26:11.607061 46bc [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/06/19-19:26:11.607075 46bc [db\version_set.cc:5588] Column family [default] (ID 0), log number is 0
2025/06/19-19:26:11.607459 46bc [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9d-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-19:26:11.608534 46bc [db\version_set.cc:5051] Creating manifest 5
2025/06/19-19:26:11.611079 46bc [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-19:26:11.611088 46bc               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:26:11.611091 46bc           Options.merge_operator: StringAppendOperator
2025/06/19-19:26:11.611093 46bc        Options.compaction_filter: None
2025/06/19-19:26:11.611095 46bc        Options.compaction_filter_factory: None
2025/06/19-19:26:11.611097 46bc  Options.sst_partitioner_factory: None
2025/06/19-19:26:11.611100 46bc         Options.memtable_factory: SkipListFactory
2025/06/19-19:26:11.611102 46bc            Options.table_factory: BlockBasedTable
2025/06/19-19:26:11.611129 46bc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001366C4B36B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001367064F6B0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:26:11.611134 46bc        Options.write_buffer_size: 67108864
2025/06/19-19:26:11.611138 46bc  Options.max_write_buffer_number: 3
2025/06/19-19:26:11.611140 46bc          Options.compression: Snappy
2025/06/19-19:26:11.611143 46bc                  Options.bottommost_compression: Disabled
2025/06/19-19:26:11.611145 46bc       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:26:11.611147 46bc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:26:11.611150 46bc             Options.num_levels: 7
2025/06/19-19:26:11.611152 46bc        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:26:11.611154 46bc     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:26:11.611156 46bc     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:26:11.611158 46bc            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:26:11.611160 46bc                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:26:11.611163 46bc               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:26:11.611165 46bc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.611168 46bc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.611170 46bc         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:26:11.611173 46bc                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:26:11.611175 46bc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.611177 46bc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.611179 46bc            Options.compression_opts.window_bits: -14
2025/06/19-19:26:11.611182 46bc                  Options.compression_opts.level: 32767
2025/06/19-19:26:11.611189 46bc               Options.compression_opts.strategy: 0
2025/06/19-19:26:11.611191 46bc         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:26:11.611193 46bc         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:26:11.611195 46bc         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:26:11.611197 46bc         Options.compression_opts.parallel_threads: 1
2025/06/19-19:26:11.611200 46bc                  Options.compression_opts.enabled: false
2025/06/19-19:26:11.611202 46bc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:26:11.611204 46bc      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:26:11.611206 46bc          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:26:11.611208 46bc              Options.level0_stop_writes_trigger: 40
2025/06/19-19:26:11.611210 46bc                   Options.target_file_size_base: 67108864
2025/06/19-19:26:11.611212 46bc             Options.target_file_size_multiplier: 1
2025/06/19-19:26:11.611215 46bc                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:26:11.611217 46bc Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:26:11.611219 46bc          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:26:11.611222 46bc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:26:11.611224 46bc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:26:11.611226 46bc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:26:11.611228 46bc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:26:11.611230 46bc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:26:11.611233 46bc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:26:11.611235 46bc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:26:11.611237 46bc       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:26:11.611239 46bc                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:26:11.611242 46bc                        Options.arena_block_size: 1048576
2025/06/19-19:26:11.611245 46bc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:26:11.611247 46bc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:26:11.611250 46bc                Options.disable_auto_compactions: 0
2025/06/19-19:26:11.611253 46bc                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:26:11.611255 46bc                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:26:11.611258 46bc Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:26:11.611260 46bc Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:26:11.611262 46bc Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:26:11.611264 46bc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:26:11.611266 46bc Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:26:11.611269 46bc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:26:11.611271 46bc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:26:11.611274 46bc Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:26:11.611277 46bc                   Options.table_properties_collectors: 
2025/06/19-19:26:11.611280 46bc                   Options.inplace_update_support: 0
2025/06/19-19:26:11.611282 46bc                 Options.inplace_update_num_locks: 10000
2025/06/19-19:26:11.611284 46bc               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:26:11.611286 46bc               Options.memtable_whole_key_filtering: 0
2025/06/19-19:26:11.611289 46bc   Options.memtable_huge_page_size: 0
2025/06/19-19:26:11.611291 46bc                           Options.bloom_locality: 0
2025/06/19-19:26:11.611293 46bc                    Options.max_successive_merges: 0
2025/06/19-19:26:11.611295 46bc                Options.optimize_filters_for_hits: 0
2025/06/19-19:26:11.611297 46bc                Options.paranoid_file_checks: 0
2025/06/19-19:26:11.611299 46bc                Options.force_consistency_checks: 1
2025/06/19-19:26:11.611301 46bc                Options.report_bg_io_stats: 0
2025/06/19-19:26:11.611303 46bc                               Options.ttl: 2592000
2025/06/19-19:26:11.611306 46bc          Options.periodic_compaction_seconds: 0
2025/06/19-19:26:11.611308 46bc  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:26:11.611310 46bc                       Options.enable_blob_files: false
2025/06/19-19:26:11.611312 46bc                           Options.min_blob_size: 0
2025/06/19-19:26:11.611314 46bc                          Options.blob_file_size: 268435456
2025/06/19-19:26:11.611316 46bc                   Options.blob_compression_type: NoCompression
2025/06/19-19:26:11.611318 46bc          Options.enable_blob_garbage_collection: false
2025/06/19-19:26:11.611321 46bc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:26:11.611323 46bc Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:26:11.611325 46bc          Options.blob_compaction_readahead_size: 0
2025/06/19-19:26:11.611327 46bc                Options.blob_file_starting_level: 0
2025/06/19-19:26:11.611330 46bc Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:26:11.612847 46bc [db\db_impl\db_impl.cc:3086] Created column family [Configuration] (ID 1)
2025/06/19-19:26:11.620550 46bc [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000001366D515370
2025/06/19-19:26:11.620924 46bc DB pointer 0000013670A1C040
2025/06/19-19:26:11.621433 2b64 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-19:26:11.621446 2b64 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001367064F6B0#5236 capacity: 512.00 MB usage: 0.08 KB table_size: 4096 occupancy: 1 collections: 1 last_copies: 1 last_secs: 0.000152 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001367064F6B0#5236 capacity: 512.00 MB usage: 0.08 KB table_size: 4096 occupancy: 1 collections: 1 last_copies: 1 last_secs: 0.000152 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [Configuration] **
2025/06/19-19:26:11.622137 2b64 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 0
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 0
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 0
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 0
rocksdb.block.cache.index.bytes.insert COUNT : 0
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 0
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 0
rocksdb.block.cache.data.bytes.insert COUNT : 0
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 0
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 0
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 0
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 0
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 0
rocksdb.non.last.level.read.count COUNT : 0
rocksdb.block.checksum.compute.count COUNT : 0
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 347.500000 P95 : 386.000000 P99 : 386.000000 P100 : 386.000000 COUNT : 3 SUM : 1056
rocksdb.table.open.io.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.write.raw.block.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-19:26:12.622325 2b64 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-19:26:12.622354 2b64 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-19:26:12.622360 2b64 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
