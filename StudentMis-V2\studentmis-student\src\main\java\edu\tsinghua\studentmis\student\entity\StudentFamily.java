package edu.tsinghua.studentmis.student.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import edu.tsinghua.studentmis.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 学生家庭信息实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stu_family_info")
@Schema(description = "学生家庭信息")
public class StudentFamily extends BaseEntity {

    @Schema(description = "学生ID")
    @NotNull(message = "学生ID不能为空")
    @TableField("student_id")
    private Long studentId;

    @Schema(description = "关系类型")
    @NotNull(message = "关系类型不能为空")
    @TableField("relation_type")
    private RelationType relationType;

    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    @TableField("name")
    private String name;

    @Schema(description = "电话")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "电话格式不正确")
    @TableField("phone")
    private String phone;

    @Schema(description = "职业")
    @Size(max = 100, message = "职业长度不能超过100个字符")
    @TableField("occupation")
    private String occupation;

    @Schema(description = "工作单位")
    @Size(max = 200, message = "工作单位长度不能超过200个字符")
    @TableField("work_unit")
    private String workUnit;

    @Schema(description = "地址")
    @Size(max = 255, message = "地址长度不能超过255个字符")
    @TableField("address")
    private String address;

    @Schema(description = "是否主要联系人")
    @TableField("is_primary")
    private Boolean isPrimary;

    /**
     * 关系类型枚举
     */
    public enum RelationType {
        FATHER("父亲"),
        MOTHER("母亲"),
        GUARDIAN("监护人"),
        OTHER("其他");

        private final String description;

        RelationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查是否为主要联系人
     */
    public boolean isPrimaryContact() {
        return Boolean.TRUE.equals(this.isPrimary);
    }
}
