import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    redirect: '/dashboard',
    component: () => import('@/layout/index.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard'
        }
      }
    ]
  },
  {
    path: '/student',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '学生管理',
      icon: 'User',
      requiresAuth: true
    },
    children: [
      {
        path: 'list',
        name: 'StudentList',
        component: () => import('@/views/student/list/index.vue'),
        meta: {
          title: '学生列表',
          icon: 'UserFilled'
        }
      },
      {
        path: 'detail/:id',
        name: 'StudentDetail',
        component: () => import('@/views/student/detail/index.vue'),
        meta: {
          title: '学生详情',
          hidden: true
        }
      },
      {
        path: 'family/:id',
        name: 'StudentFamily',
        component: () => import('@/views/student/family/index.vue'),
        meta: {
          title: '家庭信息',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/grade',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '成绩管理',
      icon: 'Document',
      requiresAuth: true
    },
    children: [
      {
        path: 'list',
        name: 'GradeList',
        component: () => import('@/views/grade/list/index.vue'),
        meta: {
          title: '成绩列表',
          icon: 'DocumentChecked'
        }
      },
      {
        path: 'input',
        name: 'GradeInput',
        component: () => import('@/views/grade/input/index.vue'),
        meta: {
          title: '成绩录入',
          icon: 'EditPen'
        }
      },
      {
        path: 'statistics',
        name: 'GradeStatistics',
        component: () => import('@/views/grade/statistics/index.vue'),
        meta: {
          title: '成绩统计',
          icon: 'DataAnalysis'
        }
      }
    ]
  },
  {
    path: '/course',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '课程管理',
      icon: 'Reading',
      requiresAuth: true
    },
    children: [
      {
        path: 'list',
        name: 'CourseList',
        component: () => import('@/views/course/list/index.vue'),
        meta: {
          title: '课程列表',
          icon: 'Notebook'
        }
      },
      {
        path: 'schedule',
        name: 'CourseSchedule',
        component: () => import('@/views/course/schedule/index.vue'),
        meta: {
          title: '课程安排',
          icon: 'Calendar'
        }
      }
    ]
  },
  {
    path: '/analytics',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '数据分析',
      icon: 'TrendCharts',
      requiresAuth: true
    },
    children: [
      {
        path: 'overview',
        name: 'AnalyticsOverview',
        component: () => import('@/views/analytics/overview/index.vue'),
        meta: {
          title: '数据概览',
          icon: 'PieChart'
        }
      },
      {
        path: 'prediction',
        name: 'GradePrediction',
        component: () => import('@/views/analytics/prediction/index.vue'),
        meta: {
          title: '成绩预测',
          icon: 'TrendCharts'
        }
      }
    ]
  },
  {
    path: '/system',
    component: () => import('@/layout/index.vue'),
    meta: {
      title: '系统管理',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['ADMIN', 'SUPER_ADMIN']
    },
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/views/system/user/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'User'
        }
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/views/system/role/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'Avatar'
        }
      }
    ]
  },
  {
    path: '/profile',
    component: () => import('@/layout/index.vue'),
    meta: {
      requiresAuth: true,
      hidden: true
    },
    children: [
      {
        path: '',
        name: 'Profile',
        component: () => import('@/views/profile/index.vue'),
        meta: {
          title: '个人中心'
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior: () => ({ top: 0 })
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - StudentMIS V2`
  }
  
  // 检查是否需要认证
  if (requiresAuth) {
    if (!userStore.token) {
      ElMessage.warning('请先登录')
      next('/login')
      return
    }
    
    // 检查用户信息
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        userStore.logout()
        next('/login')
        return
      }
    }
    
    // 检查角色权限
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      const hasRole = to.meta.roles.some(role => userStore.roles.includes(role))
      if (!hasRole) {
        ElMessage.error('权限不足')
        next('/404')
        return
      }
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.token) {
    next('/')
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
