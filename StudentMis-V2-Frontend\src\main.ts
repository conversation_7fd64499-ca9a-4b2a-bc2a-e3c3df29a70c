import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import { setupDirectives } from './directives'
import { setupGlobalComponents } from './components'

// 样式
import '@/styles/index.scss'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 安装插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

// 设置全局组件
setupGlobalComponents(app)

// 设置自定义指令
setupDirectives(app)

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🎉 StudentMIS V2 Frontend Started')
  console.log('📚 清华大学级学生成绩管理系统')
  console.log('🚀 Vue 3 + TypeScript + Element Plus')
}
