package edu.tsinghua.studentmis.grade;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 成绩管理服务启动类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@SpringBootApplication(scanBasePackages = {"edu.tsinghua.studentmis"})
@EnableDiscoveryClient
@MapperScan("edu.tsinghua.studentmis.grade.mapper")
public class GradeApplication {

    public static void main(String[] args) {
        SpringApplication.run(GradeApplication.class, args);
        System.out.println("========================================");
        System.out.println("  StudentMIS V2 Grade Service Started  ");
        System.out.println("  清华大学级学生成绩管理系统 - 成绩服务  ");
        System.out.println("========================================");
    }
}
