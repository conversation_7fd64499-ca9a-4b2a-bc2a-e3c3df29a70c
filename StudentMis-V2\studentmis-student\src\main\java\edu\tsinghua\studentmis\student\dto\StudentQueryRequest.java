package edu.tsinghua.studentmis.student.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import edu.tsinghua.studentmis.student.entity.Student;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDate;

/**
 * 学生查询请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "学生查询请求")
public class StudentQueryRequest {

    @Schema(description = "当前页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    @Schema(description = "学号")
    private String studentId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private Student.Gender gender;

    @Schema(description = "学籍状态")
    private Student.StudentStatus status;

    @Schema(description = "专业ID")
    private Long majorId;

    @Schema(description = "班级ID")
    private Long classId;

    @Schema(description = "院系ID")
    private Long departmentId;

    @Schema(description = "入学日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate admissionDateStart;

    @Schema(description = "入学日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate admissionDateEnd;

    @Schema(description = "年级")
    private Integer grade;

    @Schema(description = "排序字段", example = "createdAt")
    private String sortField = "createdAt";

    @Schema(description = "排序方向", example = "desc")
    private String sortOrder = "desc";
}
