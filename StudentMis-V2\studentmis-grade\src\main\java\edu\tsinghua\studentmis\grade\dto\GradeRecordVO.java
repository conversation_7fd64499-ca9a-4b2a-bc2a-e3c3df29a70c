package edu.tsinghua.studentmis.grade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import edu.tsinghua.studentmis.grade.entity.GradeRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 成绩记录VO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "成绩记录视图对象")
public class GradeRecordVO {

    @Schema(description = "成绩记录ID")
    private Long id;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学号")
    private String studentNumber;

    @Schema(description = "课程安排ID")
    private Long scheduleId;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程编码")
    private String courseCode;

    @Schema(description = "学分")
    private BigDecimal credits;

    @Schema(description = "成绩类型")
    private GradeRecord.GradeType gradeType;

    @Schema(description = "成绩类型描述")
    private String gradeTypeDesc;

    @Schema(description = "平时成绩")
    private BigDecimal usualScore;

    @Schema(description = "期中成绩")
    private BigDecimal midtermScore;

    @Schema(description = "期末成绩")
    private BigDecimal finalScore;

    @Schema(description = "总成绩")
    private BigDecimal totalScore;

    @Schema(description = "绩点")
    private BigDecimal gradePoint;

    @Schema(description = "等级成绩")
    private String letterGrade;

    @Schema(description = "是否通过")
    private Boolean isPass;

    @Schema(description = "班级排名")
    private Integer rankInClass;

    @Schema(description = "百分位数")
    private BigDecimal percentile;

    @Schema(description = "考试日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate examDate;

    @Schema(description = "录入人ID")
    private Long inputBy;

    @Schema(description = "录入人姓名")
    private String inputByName;

    @Schema(description = "录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inputTime;

    @Schema(description = "审核人ID")
    private Long auditBy;

    @Schema(description = "审核人姓名")
    private String auditByName;

    @Schema(description = "审核状态")
    private GradeRecord.AuditStatus auditStatus;

    @Schema(description = "审核状态描述")
    private String auditStatusDesc;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    @Schema(description = "审核意见")
    private String auditComment;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 设置成绩类型描述
    public void setGradeType(GradeRecord.GradeType gradeType) {
        this.gradeType = gradeType;
        this.gradeTypeDesc = gradeType != null ? gradeType.getDescription() : null;
    }

    // 设置审核状态描述
    public void setAuditStatus(GradeRecord.AuditStatus auditStatus) {
        this.auditStatus = auditStatus;
        this.auditStatusDesc = auditStatus != null ? auditStatus.getDescription() : null;
    }
}
