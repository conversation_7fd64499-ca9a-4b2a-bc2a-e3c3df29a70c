<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

const userStore = useUserStore()
const appStore = useAppStore()

onMounted(async () => {
  // 初始化应用
  await appStore.initApp()
  
  // 检查用户登录状态
  if (userStore.token) {
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      userStore.logout()
    }
  }
})
</script>

<style lang="scss">
#app {
  height: 100vh;
  width: 100vw;
}

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Element Plus 样式覆盖
.el-message {
  min-width: 300px;
}

.el-notification {
  min-width: 330px;
}

// 表格样式
.el-table {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
}

// 卡片样式
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 按钮样式
.el-button {
  border-radius: 6px;
}

// 输入框样式
.el-input__wrapper {
  border-radius: 6px;
}

// 选择器样式
.el-select .el-input__wrapper {
  border-radius: 6px;
}

// 日期选择器样式
.el-date-editor .el-input__wrapper {
  border-radius: 6px;
}
</style>
