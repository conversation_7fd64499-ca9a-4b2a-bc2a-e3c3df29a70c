import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 设备类型
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  
  // 主题模式
  const isDark = ref(false)
  
  // 语言
  const language = ref('zh-CN')
  
  // 页面加载状态
  const pageLoading = ref(false)
  
  // 全局配置
  const config = ref({
    title: 'StudentMIS V2',
    description: '清华大学级学生成绩管理系统',
    version: '2.0.0',
    copyright: '© 2024 Tsinghua University'
  })

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  // 设置设备类型
  const setDevice = (deviceType: 'desktop' | 'tablet' | 'mobile') => {
    device.value = deviceType
    
    // 移动端自动收起侧边栏
    if (deviceType === 'mobile') {
      sidebarCollapsed.value = true
    }
  }

  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    
    // 更新HTML类名
    const html = document.documentElement
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
    
    // 保存到本地存储
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  }

  // 设置主题
  const setTheme = (theme: 'light' | 'dark') => {
    isDark.value = theme === 'dark'
    
    const html = document.documentElement
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
    
    localStorage.setItem('theme', theme)
  }

  // 设置语言
  const setLanguage = (lang: string) => {
    language.value = lang
    localStorage.setItem('language', lang)
  }

  // 设置页面加载状态
  const setPageLoading = (loading: boolean) => {
    pageLoading.value = loading
  }

  // 初始化应用
  const initApp = async () => {
    // 从本地存储恢复设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      setTheme(savedTheme as 'light' | 'dark')
    }
    
    const savedLanguage = localStorage.getItem('language')
    if (savedLanguage) {
      setLanguage(savedLanguage)
    }
    
    const savedSidebarState = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarState) {
      setSidebarCollapsed(JSON.parse(savedSidebarState))
    }
    
    // 检测设备类型
    const checkDevice = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDevice('mobile')
      } else if (width < 1024) {
        setDevice('tablet')
      } else {
        setDevice('desktop')
      }
    }
    
    checkDevice()
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkDevice)
  }

  // 保存侧边栏状态到本地存储
  const saveSidebarState = () => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(sidebarCollapsed.value))
  }

  return {
    // 状态
    sidebarCollapsed,
    device,
    isDark,
    language,
    pageLoading,
    config,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setDevice,
    toggleTheme,
    setTheme,
    setLanguage,
    setPageLoading,
    initApp,
    saveSidebarState
  }
})
