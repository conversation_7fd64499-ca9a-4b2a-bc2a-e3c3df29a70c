import Cookies from 'js-cookie'
import CryptoJS from 'crypto-js'

// Token相关常量
const TOKEN_KEY = 'studentmis_token'
const REFRESH_TOKEN_KEY = 'studentmis_refresh_token'
const USER_INFO_KEY = 'studentmis_user_info'

// 加密密钥
const SECRET_KEY = 'studentmis-v2-secret'

/**
 * 加密数据
 */
const encrypt = (data: string): string => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString()
}

/**
 * 解密数据
 */
const decrypt = (encryptedData: string): string => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY)
    return bytes.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('解密失败:', error)
    return ''
  }
}

/**
 * 获取Token
 */
export const getToken = (): string => {
  const encryptedToken = Cookies.get(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY)
  if (!encryptedToken) return ''
  
  return decrypt(encryptedToken)
}

/**
 * 设置Token
 */
export const setToken = (token: string, remember = false): void => {
  const encryptedToken = encrypt(token)
  
  if (remember) {
    // 记住登录状态，使用Cookie存储（7天）
    Cookies.set(TOKEN_KEY, encryptedToken, { expires: 7 })
  } else {
    // 不记住登录状态，使用sessionStorage存储
    localStorage.setItem(TOKEN_KEY, encryptedToken)
  }
}

/**
 * 移除Token
 */
export const removeToken = (): void => {
  Cookies.remove(TOKEN_KEY)
  localStorage.removeItem(TOKEN_KEY)
  sessionStorage.removeItem(TOKEN_KEY)
}

/**
 * 获取刷新Token
 */
export const getRefreshToken = (): string => {
  const encryptedToken = Cookies.get(REFRESH_TOKEN_KEY) || localStorage.getItem(REFRESH_TOKEN_KEY)
  if (!encryptedToken) return ''
  
  return decrypt(encryptedToken)
}

/**
 * 设置刷新Token
 */
export const setRefreshToken = (token: string, remember = false): void => {
  const encryptedToken = encrypt(token)
  
  if (remember) {
    Cookies.set(REFRESH_TOKEN_KEY, encryptedToken, { expires: 30 })
  } else {
    localStorage.setItem(REFRESH_TOKEN_KEY, encryptedToken)
  }
}

/**
 * 移除刷新Token
 */
export const removeRefreshToken = (): void => {
  Cookies.remove(REFRESH_TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
  sessionStorage.removeItem(REFRESH_TOKEN_KEY)
}

/**
 * 获取用户信息
 */
export const getUserInfo = (): any => {
  const encryptedUserInfo = localStorage.getItem(USER_INFO_KEY)
  if (!encryptedUserInfo) return null
  
  try {
    const userInfoStr = decrypt(encryptedUserInfo)
    return JSON.parse(userInfoStr)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

/**
 * 设置用户信息
 */
export const setUserInfo = (userInfo: any): void => {
  const userInfoStr = JSON.stringify(userInfo)
  const encryptedUserInfo = encrypt(userInfoStr)
  localStorage.setItem(USER_INFO_KEY, encryptedUserInfo)
}

/**
 * 移除用户信息
 */
export const removeUserInfo = (): void => {
  localStorage.removeItem(USER_INFO_KEY)
}

/**
 * 清除所有认证信息
 */
export const clearAuth = (): void => {
  removeToken()
  removeRefreshToken()
  removeUserInfo()
}

/**
 * 检查Token是否过期
 */
export const isTokenExpired = (token?: string): boolean => {
  const currentToken = token || getToken()
  if (!currentToken) return true
  
  try {
    // 解析JWT Token
    const payload = JSON.parse(atob(currentToken.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    
    return payload.exp < currentTime
  } catch (error) {
    console.error('Token解析失败:', error)
    return true
  }
}

/**
 * 获取Token剩余时间（秒）
 */
export const getTokenRemainingTime = (token?: string): number => {
  const currentToken = token || getToken()
  if (!currentToken) return 0
  
  try {
    const payload = JSON.parse(atob(currentToken.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    
    return Math.max(0, payload.exp - currentTime)
  } catch (error) {
    console.error('Token解析失败:', error)
    return 0
  }
}

/**
 * 检查是否需要刷新Token（剩余时间少于5分钟）
 */
export const shouldRefreshToken = (token?: string): boolean => {
  const remainingTime = getTokenRemainingTime(token)
  return remainingTime > 0 && remainingTime < 300 // 5分钟
}
