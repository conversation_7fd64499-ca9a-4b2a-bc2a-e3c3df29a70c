@echo off
chcp 65001
echo ========================================
echo   StudentMIS V2 依赖安装脚本
echo   自动安装所需环境和依赖
echo ========================================
echo.

color 0C

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 需要管理员权限运行此脚本
    echo 请右键点击脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

:: 检查Chocolatey
echo [1/10] 检查包管理器 Chocolatey...
choco -v >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 Chocolatey...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if %errorlevel% neq 0 (
        echo ❌ Chocolatey 安装失败
        pause
        exit /b 1
    )
    echo ✅ Chocolatey 安装成功
) else (
    echo ✅ Chocolatey 已安装
)
echo.

:: 安装JDK 17
echo [2/10] 安装 JDK 17...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 OpenJDK 17...
    choco install openjdk17 -y
    if %errorlevel% neq 0 (
        echo ❌ JDK 17 安装失败
        pause
        exit /b 1
    )
    echo ✅ JDK 17 安装成功
    echo 请重新打开命令行窗口以使环境变量生效
) else (
    echo ✅ Java 已安装
)
echo.

:: 安装Maven
echo [3/10] 安装 Maven...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 Maven...
    choco install maven -y
    if %errorlevel% neq 0 (
        echo ❌ Maven 安装失败
        pause
        exit /b 1
    )
    echo ✅ Maven 安装成功
) else (
    echo ✅ Maven 已安装
)
echo.

:: 安装Node.js
echo [4/10] 安装 Node.js...
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 Node.js 18 LTS...
    choco install nodejs-lts -y
    if %errorlevel% neq 0 (
        echo ❌ Node.js 安装失败
        pause
        exit /b 1
    )
    echo ✅ Node.js 安装成功
) else (
    echo ✅ Node.js 已安装
)
echo.

:: 安装MySQL
echo [5/10] 安装 MySQL...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 MySQL 8.0...
    choco install mysql -y
    if %errorlevel% neq 0 (
        echo ❌ MySQL 安装失败，请手动安装
        echo 下载地址: https://dev.mysql.com/downloads/installer/
        pause
    ) else (
        echo ✅ MySQL 安装成功
        echo ⚠️  请记住设置的root密码
    )
) else (
    echo ✅ MySQL 已安装
)
echo.

:: 安装Redis
echo [6/10] 安装 Redis...
redis-cli --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 Redis...
    choco install redis-64 -y
    if %errorlevel% neq 0 (
        echo ❌ Redis 安装失败，请手动安装
        echo 下载地址: https://github.com/microsoftarchive/redis/releases
        pause
    ) else (
        echo ✅ Redis 安装成功
    )
) else (
    echo ✅ Redis 已安装
)
echo.

:: 安装Git
echo [7/10] 安装 Git...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 Git...
    choco install git -y
    if %errorlevel% neq 0 (
        echo ❌ Git 安装失败
        pause
        exit /b 1
    )
    echo ✅ Git 安装成功
) else (
    echo ✅ Git 已安装
)
echo.

:: 安装curl
echo [8/10] 安装 curl...
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 curl...
    choco install curl -y
    if %errorlevel% neq 0 (
        echo ❌ curl 安装失败
        pause
        exit /b 1
    )
    echo ✅ curl 安装成功
) else (
    echo ✅ curl 已安装
)
echo.

:: 下载Nacos
echo [9/10] 下载 Nacos...
if not exist "nacos" (
    echo 正在下载 Nacos 2.3.0...
    curl -L -o nacos-server-2.3.0.zip "https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.zip"
    if %errorlevel% neq 0 (
        echo ❌ Nacos 下载失败，请手动下载
        echo 下载地址: https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.zip
        pause
    ) else (
        echo 正在解压 Nacos...
        powershell -Command "Expand-Archive -Path nacos-server-2.3.0.zip -DestinationPath . -Force"
        del nacos-server-2.3.0.zip
        echo ✅ Nacos 下载并解压成功
    )
) else (
    echo ✅ Nacos 已存在
)
echo.

:: 配置环境
echo [10/10] 配置环境...

:: 创建MySQL数据库
echo 配置MySQL数据库...
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS studentmis_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; CREATE USER IF NOT EXISTS 'studentmis'@'localhost' IDENTIFIED BY 'StudentMIS@2024'; GRANT ALL PRIVILEGES ON studentmis_v2.* TO 'studentmis'@'localhost'; FLUSH PRIVILEGES;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL数据库配置成功
) else (
    echo ⚠️  MySQL数据库配置可能失败，请手动配置
)

:: 启动Redis服务
echo 启动Redis服务...
net start redis >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis服务启动成功
) else (
    echo ⚠️  Redis服务启动可能失败，请手动启动
)

echo.
echo ========================================
echo   🎉 依赖安装完成！
echo ========================================
echo.
echo 📋 已安装的组件:
echo   ✅ JDK 17
echo   ✅ Maven 3.8+
echo   ✅ Node.js 18 LTS
echo   ✅ MySQL 8.0
echo   ✅ Redis 7.0
echo   ✅ Git
echo   ✅ curl
echo   ✅ Nacos 2.3.0
echo.
echo 📝 下一步操作:
echo   1. 重新打开命令行窗口（刷新环境变量）
echo   2. 运行 start-all.bat 启动系统
echo   3. 运行 test-system.bat 测试系统
echo.
echo ⚠️  注意事项:
echo   1. 请确保MySQL root密码已设置
echo   2. 如果某些组件安装失败，请手动安装
echo   3. 防火墙可能需要允许相关端口访问
echo.

pause
