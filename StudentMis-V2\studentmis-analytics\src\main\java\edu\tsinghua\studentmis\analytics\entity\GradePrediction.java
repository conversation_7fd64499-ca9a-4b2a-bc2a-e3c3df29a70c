package edu.tsinghua.studentmis.analytics.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成绩预测实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("grade_prediction")
public class GradePrediction {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private Long studentId;

    /**
     * 课程安排ID
     */
    @TableField("schedule_id")
    private Long scheduleId;

    /**
     * 预测类型
     */
    @TableField("prediction_type")
    private PredictionType predictionType;

    /**
     * 预测分数
     */
    @TableField("predicted_score")
    private BigDecimal predictedScore;

    /**
     * 置信度
     */
    @TableField("confidence_level")
    private BigDecimal confidenceLevel;

    /**
     * 模型版本
     */
    @TableField("model_version")
    private String modelVersion;

    /**
     * 预测因子
     */
    @TableField("prediction_factors")
    private String predictionFactors;

    /**
     * 预测日期
     */
    @TableField("prediction_date")
    private LocalDateTime predictionDate;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 预测类型枚举
     */
    public enum PredictionType {
        MIDTERM("期中成绩"),
        FINAL("期末成绩"),
        TOTAL("总成绩");

        private final String description;

        PredictionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
