package edu.tsinghua.studentmis.student.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import edu.tsinghua.studentmis.student.dto.StudentCreateRequest;
import edu.tsinghua.studentmis.student.dto.StudentQueryRequest;
import edu.tsinghua.studentmis.student.dto.StudentUpdateRequest;
import edu.tsinghua.studentmis.student.dto.StudentVO;
import edu.tsinghua.studentmis.student.entity.Student;
import edu.tsinghua.studentmis.student.mapper.StudentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 学生服务实现类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StudentService extends ServiceImpl<StudentMapper, Student> {

    private final StudentMapper studentMapper;

    /**
     * 分页查询学生信息
     */
    public IPage<StudentVO> pageStudents(StudentQueryRequest request) {
        Page<Student> page = new Page<>(request.getCurrent(), request.getSize());
        
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        wrapper.like(StrUtil.isNotBlank(request.getStudentId()), Student::getStudentId, request.getStudentId())
               .like(StrUtil.isNotBlank(request.getName()), Student::getName, request.getName())
               .eq(request.getGender() != null, Student::getGender, request.getGender())
               .eq(request.getStatus() != null, Student::getStatus, request.getStatus())
               .eq(request.getMajorId() != null, Student::getMajorId, request.getMajorId())
               .eq(request.getClassId() != null, Student::getClassId, request.getClassId())
               .ge(request.getAdmissionDateStart() != null, Student::getAdmissionDate, request.getAdmissionDateStart())
               .le(request.getAdmissionDateEnd() != null, Student::getAdmissionDate, request.getAdmissionDateEnd())
               .orderByDesc(Student::getCreatedAt);
        
        IPage<Student> studentPage = studentMapper.selectPage(page, wrapper);
        
        // 转换为VO
        return studentPage.convert(this::convertToVO);
    }

    /**
     * 根据ID获取学生详细信息
     */
    public StudentVO getStudentById(Long id) {
        Student student = studentMapper.selectById(id);
        if (student == null) {
            throw new BusinessException(ResultCode.STUDENT_NOT_FOUND);
        }
        
        return convertToVO(student);
    }

    /**
     * 根据学号获取学生信息
     */
    public StudentVO getStudentByStudentId(String studentId) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getStudentId, studentId);
        
        Student student = studentMapper.selectOne(wrapper);
        if (student == null) {
            throw new BusinessException(ResultCode.STUDENT_NOT_FOUND);
        }
        
        return convertToVO(student);
    }

    /**
     * 创建学生信息
     */
    @Transactional
    public StudentVO createStudent(StudentCreateRequest request) {
        // 检查学号是否已存在
        if (existsByStudentId(request.getStudentId())) {
            throw new BusinessException(ResultCode.STUDENT_ALREADY_EXISTS, "学号已存在");
        }
        
        // 检查身份证号是否已存在
        if (StrUtil.isNotBlank(request.getIdCard()) && existsByIdCard(request.getIdCard())) {
            throw new BusinessException(ResultCode.STUDENT_ALREADY_EXISTS, "身份证号已存在");
        }
        
        Student student = new Student();
        BeanUtil.copyProperties(request, student);
        
        // 设置默认值
        if (student.getStatus() == null) {
            student.setStatus(Student.StudentStatus.ACTIVE);
        }
        if (StrUtil.isBlank(student.getNationality())) {
            student.setNationality("中国");
        }
        
        // 保存学生信息
        studentMapper.insert(student);
        
        log.info("创建学生成功: studentId={}, name={}", student.getStudentId(), student.getName());
        
        return convertToVO(student);
    }

    /**
     * 更新学生信息
     */
    @Transactional
    public StudentVO updateStudent(Long id, StudentUpdateRequest request) {
        Student existingStudent = studentMapper.selectById(id);
        if (existingStudent == null) {
            throw new BusinessException(ResultCode.STUDENT_NOT_FOUND);
        }
        
        // 检查学号是否被其他学生使用
        if (StrUtil.isNotBlank(request.getStudentId()) && 
            !request.getStudentId().equals(existingStudent.getStudentId()) &&
            existsByStudentId(request.getStudentId())) {
            throw new BusinessException(ResultCode.STUDENT_ALREADY_EXISTS, "学号已被其他学生使用");
        }
        
        // 检查身份证号是否被其他学生使用
        if (StrUtil.isNotBlank(request.getIdCard()) && 
            !request.getIdCard().equals(existingStudent.getIdCard()) &&
            existsByIdCard(request.getIdCard())) {
            throw new BusinessException(ResultCode.STUDENT_ALREADY_EXISTS, "身份证号已被其他学生使用");
        }
        
        // 更新学生信息
        BeanUtil.copyProperties(request, existingStudent, "id", "createdAt", "createdBy");
        studentMapper.updateById(existingStudent);
        
        log.info("更新学生成功: id={}, studentId={}", id, existingStudent.getStudentId());
        
        return convertToVO(existingStudent);
    }

    /**
     * 删除学生信息
     */
    @Transactional
    public void deleteStudent(Long id) {
        Student student = studentMapper.selectById(id);
        if (student == null) {
            throw new BusinessException(ResultCode.STUDENT_NOT_FOUND);
        }
        
        // 逻辑删除
        studentMapper.deleteById(id);
        
        log.info("删除学生成功: id={}, studentId={}", id, student.getStudentId());
    }

    /**
     * 批量删除学生信息
     */
    @Transactional
    public void deleteStudents(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "删除ID列表不能为空");
        }
        
        studentMapper.deleteBatchIds(ids);
        
        log.info("批量删除学生成功: count={}", ids.size());
    }

    /**
     * 更新学生状态
     */
    @Transactional
    public void updateStudentStatus(Long id, Student.StudentStatus status) {
        Student student = studentMapper.selectById(id);
        if (student == null) {
            throw new BusinessException(ResultCode.STUDENT_NOT_FOUND);
        }
        
        student.setStatus(status);
        
        // 如果是毕业状态，设置毕业日期
        if (Student.StudentStatus.GRADUATED.equals(status) && student.getGraduationDate() == null) {
            student.setGraduationDate(LocalDate.now());
        }
        
        studentMapper.updateById(student);
        
        log.info("更新学生状态成功: id={}, status={}", id, status);
    }

    /**
     * 检查学号是否存在
     */
    private boolean existsByStudentId(String studentId) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getStudentId, studentId);
        return studentMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查身份证号是否存在
     */
    private boolean existsByIdCard(String idCard) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getIdCard, idCard);
        return studentMapper.selectCount(wrapper) > 0;
    }

    /**
     * 转换为VO对象
     */
    private StudentVO convertToVO(Student student) {
        StudentVO vo = new StudentVO();
        BeanUtil.copyProperties(student, vo);
        
        // 设置计算字段
        vo.setAge(student.getAge());
        vo.setSchoolYears(student.getSchoolYears());
        
        return vo;
    }
}
