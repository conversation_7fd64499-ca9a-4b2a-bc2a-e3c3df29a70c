package edu.tsinghua.studentmis.grade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 成绩审核请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "成绩审核请求")
public class GradeAuditRequest {

    @Schema(description = "成绩记录ID", required = true)
    @NotNull(message = "成绩记录ID不能为空")
    private Long id;

    @Schema(description = "审核结果", required = true, allowableValues = {"APPROVED", "REJECTED"})
    @NotBlank(message = "审核结果不能为空")
    private String auditResult;

    @Schema(description = "审核意见")
    private String auditComment;

    @Schema(description = "批量审核的成绩记录ID列表")
    private List<Long> batchIds;

    @Schema(description = "批量审核结果", allowableValues = {"APPROVED", "REJECTED"})
    private String batchAuditResult;

    @Schema(description = "批量审核意见")
    private String batchAuditComment;

    /**
     * 审核结果枚举
     */
    public enum AuditResult {
        APPROVED("通过"),
        REJECTED("拒绝");

        private final String description;

        AuditResult(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
