version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: studentmis-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: StudentMIS@2024
      MYSQL_DATABASE: studentmis_v2
      MYSQL_USER: studentmis
      MYSQL_PASSWORD: StudentMIS@2024
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./studentmis_v2_database.sql:/docker-entrypoint-initdb.d/init.sql
      - ./docker/mysql/conf.d:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - studentmis-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: studentmis-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - studentmis-network

  # Nacos服务注册与配置中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: studentmis-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: StudentMIS@2024
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
      JVM_XMS: 512m
      JVM_XMX: 512m
      JVM_XMN: 256m
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
      - nacos_logs:/home/<USER>/logs
    depends_on:
      - mysql
    networks:
      - studentmis-network

  # API网关
  gateway:
    build:
      context: ./studentmis-gateway
      dockerfile: Dockerfile
    container_name: studentmis-gateway
    restart: always
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - nacos
      - redis
    networks:
      - studentmis-network

  # 认证服务
  auth-service:
    build:
      context: ./studentmis-auth
      dockerfile: Dockerfile
    container_name: studentmis-auth
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: studentmis_v2
      MYSQL_USERNAME: studentmis
      MYSQL_PASSWORD: StudentMIS@2024
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - studentmis-network

  # 学生服务
  student-service:
    build:
      context: ./studentmis-student
      dockerfile: Dockerfile
    container_name: studentmis-student
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: studentmis_v2
      MYSQL_USERNAME: studentmis
      MYSQL_PASSWORD: StudentMIS@2024
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - studentmis-network

  # 成绩服务
  grade-service:
    build:
      context: ./studentmis-grade
      dockerfile: Dockerfile
    container_name: studentmis-grade
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: studentmis_v2
      MYSQL_USERNAME: studentmis
      MYSQL_PASSWORD: StudentMIS@2024
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - studentmis-network

  # 数据分析服务
  analytics-service:
    build:
      context: ./studentmis-analytics
      dockerfile: Dockerfile
    container_name: studentmis-analytics
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: studentmis_v2
      MYSQL_USERNAME: studentmis
      MYSQL_PASSWORD: StudentMIS@2024
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - studentmis-network

  # 前端应用
  frontend:
    build:
      context: ../StudentMis-V2-Frontend
      dockerfile: Dockerfile
    container_name: studentmis-frontend
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - gateway
    networks:
      - studentmis-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: studentmis-prometheus
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - studentmis-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: studentmis-grafana
    restart: always
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - studentmis-network

  # ELK日志收集
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: studentmis-elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - studentmis-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: studentmis-kibana
    restart: always
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - studentmis-network

volumes:
  mysql_data:
  redis_data:
  nacos_data:
  nacos_logs:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  studentmis-network:
    driver: bridge
