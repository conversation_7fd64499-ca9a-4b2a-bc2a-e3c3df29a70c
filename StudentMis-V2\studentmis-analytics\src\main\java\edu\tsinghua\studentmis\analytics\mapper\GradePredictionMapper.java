package edu.tsinghua.studentmis.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import edu.tsinghua.studentmis.analytics.entity.GradePrediction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 成绩预测Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface GradePredictionMapper extends BaseMapper<GradePrediction> {

    /**
     * 获取历史成绩数据
     */
    @Select("SELECT student_id, usual_score, midterm_score, final_score, total_score " +
            "FROM grade_record WHERE schedule_id = #{scheduleId} AND status = 'PUBLISHED'")
    List<Map<String, Object>> getHistoricalGradeData(@Param("scheduleId") Long scheduleId);

    /**
     * 获取当前平时成绩
     */
    @Select("SELECT usual_score FROM grade_record " +
            "WHERE student_id = #{studentId} AND schedule_id = #{scheduleId}")
    Double getCurrentUsualScore(@Param("studentId") Long studentId, 
                               @Param("scheduleId") Long scheduleId);

    /**
     * 获取当前各项成绩
     */
    @Select("SELECT usual_score, midterm_score, final_score, total_score " +
            "FROM grade_record WHERE student_id = #{studentId} AND schedule_id = #{scheduleId}")
    Map<String, Object> getCurrentScores(@Param("studentId") Long studentId, 
                                        @Param("scheduleId") Long scheduleId);
}
