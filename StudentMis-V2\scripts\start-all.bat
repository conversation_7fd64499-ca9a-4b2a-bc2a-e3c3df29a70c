@echo off
chcp 65001
echo ========================================
echo   StudentMIS V2 系统启动脚本
echo   清华大学级学生成绩管理系统
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查Java环境
echo [1/8] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境，请安装JDK 17或更高版本
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Maven环境
echo [2/8] 检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Maven环境，请安装Maven 3.8或更高版本
    pause
    exit /b 1
)
echo ✅ Maven环境检查通过

:: 检查MySQL服务
echo [3/8] 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 警告: MySQL服务未运行，请启动MySQL服务
    echo 可以使用以下命令启动: net start mysql
    pause
)
echo ✅ MySQL服务检查通过

:: 检查Redis服务
echo [4/8] 检查Redis服务...
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe" >nul
if %errorlevel% neq 0 (
    echo ❌ 警告: Redis服务未运行，请启动Redis服务
    echo 可以手动启动Redis或使用: redis-server
    pause
)
echo ✅ Redis服务检查通过

:: 启动Nacos
echo [5/8] 启动Nacos服务注册中心...
if not exist "nacos\bin\startup.cmd" (
    echo ❌ 错误: 未找到Nacos，请下载并解压Nacos到当前目录
    echo 下载地址: https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.zip
    pause
    exit /b 1
)

cd nacos\bin
start "Nacos" cmd /c "startup.cmd -m standalone && pause"
cd ..\..
echo ✅ Nacos启动中... (请等待30秒)
timeout /t 30 /nobreak >nul

:: 编译项目
echo [6/8] 编译项目...
echo 正在编译，请稍候...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo ❌ 错误: 项目编译失败
    pause
    exit /b 1
)
echo ✅ 项目编译完成

:: 启动微服务
echo [7/8] 启动微服务...

echo 启动API网关...
cd studentmis-gateway
start "Gateway" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev && pause"
cd ..
timeout /t 10 /nobreak >nul

echo 启动认证服务...
cd studentmis-auth
start "Auth Service" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev && pause"
cd ..
timeout /t 10 /nobreak >nul

echo 启动学生服务...
cd studentmis-student
start "Student Service" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev && pause"
cd ..
timeout /t 10 /nobreak >nul

echo 启动成绩服务...
cd studentmis-grade
start "Grade Service" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev && pause"
cd ..
timeout /t 10 /nobreak >nul

echo 启动数据分析服务...
cd studentmis-analytics
start "Analytics Service" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev && pause"
cd ..

echo ✅ 所有微服务启动中...

:: 启动前端
echo [8/8] 启动前端应用...
if exist "..\StudentMis-V2-Frontend" (
    cd ..\StudentMis-V2-Frontend
    
    :: 检查Node.js
    node -v >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 错误: 未找到Node.js环境，请安装Node.js 18或更高版本
        pause
        exit /b 1
    )
    
    :: 检查依赖
    if not exist "node_modules" (
        echo 正在安装前端依赖...
        npm install
    )
    
    echo 启动前端开发服务器...
    start "Frontend" cmd /c "npm run dev && pause"
    cd ..\StudentMis-V2
) else (
    echo ❌ 警告: 未找到前端项目目录
)

echo.
echo ========================================
echo   🎉 StudentMIS V2 启动完成!
echo ========================================
echo.
echo 📋 服务访问地址:
echo   前端应用:     http://localhost:3000
echo   API网关:      http://localhost:8080
echo   Nacos控制台:  http://localhost:8848/nacos
echo   API文档:      http://localhost:8080/doc.html
echo.
echo 🔑 默认账号:
echo   管理员: admin / 123456
echo   学生:   2024001001 / 123456
echo   教师:   T001 / 123456
echo.
echo 📝 注意事项:
echo   1. 请确保MySQL和Redis服务正常运行
echo   2. 首次启动可能需要等待1-2分钟
echo   3. 如遇问题请查看各服务窗口的日志信息
echo.
echo 按任意键打开系统首页...
pause >nul
start http://localhost:3000

echo 系统启动脚本执行完成
pause
