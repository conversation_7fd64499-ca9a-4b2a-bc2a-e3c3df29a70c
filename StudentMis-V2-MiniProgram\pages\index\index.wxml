<!--pages/index/index.wxml-->
<view class="container">
  <!-- 顶部用户信息 -->
  <view class="user-header">
    <view class="user-avatar">
      <van-image
        width="60"
        height="60"
        round
        src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
        error-icon="user-o"
      />
    </view>
    <view class="user-info">
      <view class="user-name">{{userInfo.realName || '未登录'}}</view>
      <view class="user-id">{{userInfo.studentId || '请先登录'}}</view>
    </view>
    <view class="weather-info" wx:if="{{weather}}">
      <view class="weather-temp">{{weather.temperature}}°</view>
      <view class="weather-desc">{{weather.description}}</view>
    </view>
  </view>

  <!-- 通知公告 -->
  <view class="notice-section" wx:if="{{notices.length > 0}}">
    <van-notice-bar
      left-icon="volume-o"
      text="{{currentNotice.title}}"
      mode="closeable"
      bind:close="onNoticeClose"
      bind:click="onNoticeClick"
    />
  </view>

  <!-- 快捷功能 -->
  <view class="quick-actions">
    <view class="section-title">快捷功能</view>
    <van-grid column-num="4" border="{{false}}" gutter="10">
      <van-grid-item
        wx:for="{{quickActions}}"
        wx:key="id"
        icon="{{item.icon}}"
        text="{{item.name}}"
        bind:click="onQuickAction"
        data-action="{{item.action}}"
      />
    </van-grid>
  </view>

  <!-- 今日课程 -->
  <view class="today-courses">
    <view class="section-title">
      <text>今日课程</text>
      <text class="more-link" bind:tap="goToSchedule">查看更多</text>
    </view>
    
    <view wx:if="{{todayCourses.length === 0}}" class="empty-state">
      <van-empty description="今天没有课程安排" />
    </view>
    
    <view wx:else class="course-list">
      <view
        wx:for="{{todayCourses}}"
        wx:key="id"
        class="course-item {{item.status}}"
        bind:tap="onCourseClick"
        data-course="{{item}}"
      >
        <view class="course-time">
          <view class="time-range">{{item.startTime}}-{{item.endTime}}</view>
          <view class="time-period">第{{item.period}}节</view>
        </view>
        <view class="course-info">
          <view class="course-name">{{item.courseName}}</view>
          <view class="course-location">
            <van-icon name="location-o" size="12px" />
            <text>{{item.classroom}}</text>
          </view>
          <view class="course-teacher">{{item.teacherName}}</view>
        </view>
        <view class="course-status">
          <van-tag
            type="{{item.status === 'ongoing' ? 'primary' : item.status === 'upcoming' ? 'warning' : 'default'}}"
            size="small"
          >
            {{item.statusText}}
          </van-tag>
        </view>
      </view>
    </view>
  </view>

  <!-- 最近成绩 -->
  <view class="recent-grades">
    <view class="section-title">
      <text>最近成绩</text>
      <text class="more-link" bind:tap="goToGrades">查看更多</text>
    </view>
    
    <view wx:if="{{recentGrades.length === 0}}" class="empty-state">
      <van-empty description="暂无最新成绩" />
    </view>
    
    <view wx:else class="grade-list">
      <view
        wx:for="{{recentGrades}}"
        wx:key="id"
        class="grade-item"
        bind:tap="onGradeClick"
        data-grade="{{item}}"
      >
        <view class="grade-course">
          <view class="course-name">{{item.courseName}}</view>
          <view class="grade-type">{{item.gradeType}}</view>
        </view>
        <view class="grade-score">
          <view class="score-value {{item.scoreLevel}}">{{item.totalScore}}</view>
          <view class="score-gpa">{{item.gradePoint}}绩点</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习统计 -->
  <view class="study-stats">
    <view class="section-title">学习统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-value">{{studyStats.totalCourses}}</view>
        <view class="stat-label">本学期课程</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{studyStats.avgScore}}</view>
        <view class="stat-label">平均分</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{studyStats.gpa}}</view>
        <view class="stat-label">GPA</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{studyStats.rank}}</view>
        <view class="stat-label">专业排名</view>
      </view>
    </view>
  </view>

  <!-- 推荐内容 -->
  <view class="recommendations" wx:if="{{recommendations.length > 0}}">
    <view class="section-title">为你推荐</view>
    <view class="recommendation-list">
      <view
        wx:for="{{recommendations}}"
        wx:key="id"
        class="recommendation-item"
        bind:tap="onRecommendationClick"
        data-recommendation="{{item}}"
      >
        <view class="recommendation-icon">
          <van-icon name="{{item.icon}}" size="20px" color="#667eea" />
        </view>
        <view class="recommendation-content">
          <view class="recommendation-title">{{item.title}}</view>
          <view class="recommendation-desc">{{item.description}}</view>
        </view>
        <view class="recommendation-arrow">
          <van-icon name="arrow" size="16px" color="#c8c9cc" />
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<van-loading wx:if="{{loading}}" type="spinner" color="#667eea" vertical>
  加载中...
</van-loading>

<!-- Toast提示 -->
<van-toast id="van-toast" />

<!-- 弹窗 -->
<van-dialog id="van-dialog" />
