package edu.tsinghua.studentmis.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数校验失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误 1xxx
    // 用户相关 10xx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_DISABLED(1003, "用户已被禁用"),
    USER_LOCKED(1004, "用户已被锁定"),
    PASSWORD_ERROR(1005, "密码错误"),
    PASSWORD_EXPIRED(1006, "密码已过期"),
    LOGIN_FAILED(1007, "登录失败"),
    TOKEN_INVALID(1008, "令牌无效"),
    TOKEN_EXPIRED(1009, "令牌已过期"),
    PERMISSION_DENIED(1010, "权限不足"),

    // 学生相关 11xx
    STUDENT_NOT_FOUND(1101, "学生不存在"),
    STUDENT_ALREADY_EXISTS(1102, "学生已存在"),
    STUDENT_ID_INVALID(1103, "学号格式无效"),
    STUDENT_STATUS_INVALID(1104, "学生状态无效"),

    // 教师相关 12xx
    TEACHER_NOT_FOUND(1201, "教师不存在"),
    TEACHER_ALREADY_EXISTS(1202, "教师已存在"),
    TEACHER_ID_INVALID(1203, "工号格式无效"),

    // 课程相关 13xx
    COURSE_NOT_FOUND(1301, "课程不存在"),
    COURSE_ALREADY_EXISTS(1302, "课程已存在"),
    COURSE_CODE_INVALID(1303, "课程编码格式无效"),
    COURSE_FULL(1304, "课程已满员"),
    COURSE_NOT_OPEN(1305, "课程未开放选课"),
    COURSE_ALREADY_SELECTED(1306, "课程已选择"),

    // 成绩相关 14xx
    GRADE_NOT_FOUND(1401, "成绩不存在"),
    GRADE_ALREADY_EXISTS(1402, "成绩已存在"),
    GRADE_INVALID(1403, "成绩格式无效"),
    GRADE_AUDIT_FAILED(1404, "成绩审核失败"),
    GRADE_LOCKED(1405, "成绩已锁定"),

    // 系统相关 15xx
    SYSTEM_BUSY(1501, "系统繁忙"),
    SYSTEM_MAINTENANCE(1502, "系统维护中"),
    DATA_INTEGRITY_ERROR(1503, "数据完整性错误"),
    CONCURRENT_UPDATE_ERROR(1504, "并发更新错误"),
    FILE_UPLOAD_ERROR(1505, "文件上传失败"),
    FILE_TYPE_ERROR(1506, "文件类型不支持"),
    FILE_SIZE_ERROR(1507, "文件大小超限"),

    // 第三方服务错误 16xx
    SMS_SEND_ERROR(1601, "短信发送失败"),
    EMAIL_SEND_ERROR(1602, "邮件发送失败"),
    WECHAT_API_ERROR(1603, "微信接口调用失败"),

    // 数据分析相关 17xx
    ANALYTICS_DATA_ERROR(1701, "分析数据错误"),
    PREDICTION_MODEL_ERROR(1702, "预测模型错误"),
    RECOMMENDATION_ERROR(1703, "推荐算法错误");

    private final Integer code;
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}
